
<footer class="bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] border-t-2 border-[#514b3c] mt-6">
    <div class="container max-w-md mx-auto px-3 py-4 text-center">
        
        <div class="mb-0">
            <h3 class="text-[#fceac4] font-semibold text-sm mb-0">Echoes of Eternity</h3>
           
        </div>

        
        <div class="border-t border-[#3b3629] my-0"></div>

        

        
        <div class="border-t border-[#3b3629] my-1"></div>

        
        <div class="space-y-1">
            <p class="text-[#998d66] text-xs">
                Время: <span class="text-[#c1a96e]" id="server-time"><?php echo e(now()->format('H:i')); ?></span>
            </p>
            
        <div class="space-y-1">
           
            <p class="text-[#998d66] text-xs">
                Контакты: 
                <a href="https://t.me/yourtelegram" 
                   class="text-[#c1a96e] hover:text-[#e4d7b0] transition-colors duration-200" 
                   target="_blank" 
                   rel="noopener noreferrer">
                    @De_6a
                </a>
            </p>
        </div>
            <p class="text-[#998d66] text-xs">© 2025 Echoes of Eternity</p>
        </div>

        
        <div class="mt-1 flex justify-center space-x-2">
            <a href="<?php echo e(route('auth.register')); ?>" 
               class="px-3 py-1 text-xs bg-gradient-to-b from-[#2f473c] to-[#1e2e27] 
                      text-[#f8eac2] hover:from-[#3e5c48] hover:to-[#243c2f] 
                      border border-[#3b3629] rounded transition-all duration-200 shadow-sm hover:shadow-md">
                Регистрация
            </a>
            <a href="<?php echo e(route('auth.login')); ?>" 
               class="px-3 py-1 text-xs bg-gradient-to-b from-[#4a3c66] to-[#3a2d52] 
                      text-[#f8eac2] hover:from-[#5d4a7a] hover:to-[#4a3c66] 
                      border border-[#3b3629] rounded transition-all duration-200 shadow-sm hover:shadow-md">
                Вход
            </a>
        </div>
    </div>
</footer>


<script>
document.addEventListener('DOMContentLoaded', function() {
    function updateServerTime() {
        const timeElement = document.getElementById('server-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ru-RU', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            timeElement.textContent = timeString;
        }
    }
    
    // Обновляем время каждую минуту
    setInterval(updateServerTime, 60000);
});
</script><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/auth/footer.blade.php ENDPATH**/ ?>