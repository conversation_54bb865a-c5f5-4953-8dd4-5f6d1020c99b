<?php

namespace App\Http\Controllers\Masters;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\UserResource;
use App\Models\UserNotification;
use App\Models\PotionRecipe;
use App\Models\ActiveBrewing;
use App\Models\Potion;
use App\Models\User;
use App\Models\AlchemyIngredient;
use App\Models\AlchemyCatalyst;
use App\Models\UserCatalyst;
use App\Models\LevelThreshold;
use App\Models\AlchemyLog;
use App\Models\UserRecipe;
use App\Models\UserProfile;
use App\Models\UserAlchemyIngredient;
use App\Models\GameItem;
use App\Models\UserPotion;
use App\Models\PendingPotion;
use Illuminate\Support\Facades\Validator;

class AlchemistController extends Controller
{
    /**
     * Отображает главную страницу алхимика
     * Displays the main page of the alchemist.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        // Проверка авторизации пользователя // Check user authorization
        if (!Auth::check()) {
            return redirect()->route('auth.login')->with('error', 'Сначала авторизуйтесь.');
        }

        $user = Auth::user(); // Получаем текущего пользователя // Get the current user
        $userProfile = $user->profile; // Получаем профиль пользователя // Get user profile

        // Получаем рецепты из рюкзака пользователя
        $userRecipesCollection = UserRecipe::where('user_id', $user->id)
            ->where('location', 'inventory')
            ->where('quantity', '>', 0)
            ->with('recipe.ingredients')
            ->get();

        // Получаем ресурсы из инвентаря пользователя для проверки наличия ингредиентов
        $inventoryResources = UserResource::where('user_id', $user->id)
            ->where('location', 'inventory')
            ->where('quantity', '>', 0)
            ->with('resource')
            ->get()
            ->groupBy('resource_id')
            ->map(function ($items) {
                $firstItem = $items->first();
                $totalQuantity = $items->sum('quantity');
                return (object) [
                    'resource' => $firstItem->resource,
                    'total_quantity' => $totalQuantity,
                ];
            });

        // --- Получение ресурсов из инвентаря --- // --- Get resources from inventory ---
        // Получаем ресурсы пользователя для алхимии (тип 'alchemy') из инвентаря
        // Get user resources for alchemy (type 'alchemy') from inventory
        $inventoryResources = UserResource::where('user_id', $user->id) // Фильтр по ID пользователя // Filter by user ID
            ->where('location', 'inventory') // Только в инвентаре // Only in inventory
            ->whereHas('resource', function ($query) { // Фильтр по типу ресурса через связь // Filter by resource type via relationship
                $query->where('type', 'alchemy'); // Ресурсы типа 'алхимия' // Resources of type 'alchemy'
            })
            ->where('quantity', '>', 0) // Только те, что есть в наличии // Only those in stock
            ->with('resource') // Загружаем связанную модель ресурса // Load the related resource model
            ->get()
            ->groupBy('resource_id') // Группируем по ID ресурса для суммирования // Group by resource ID for summation
            ->map(function ($items) { // Обрабатываем каждую группу // Process each group
                $firstItem = $items->first(); // Берем первый элемент группы // Take the first item of the group
                $totalQuantity = $items->sum('quantity'); // Суммируем количество // Sum the quantity
                return (object) [ // Возвращаем объект с ресурсом и общим количеством // Return an object with the resource and total quantity
                    'resource' => $firstItem->resource,
                    'total_quantity' => $totalQuantity,
                ];
            })
            ->filter(fn($res) => $res->total_quantity > 0) // Убираем ресурсы с нулевым количеством // Remove resources with zero quantity
            ->values(); // Сбрасываем ключи массива // Reset array keys

        // --- Получение активного процесса варки из базы данных --- // --- Get active brewing process from database ---
        // Сначала проверяем и обновляем статусы варок, которые должны быть завершены
        $brewingsToUpdate = ActiveBrewing::where('user_id', $user->id)
            ->where('status', ActiveBrewing::STATUS_BREWING)
            ->where('finished_at', '<=', Carbon::now())
            ->get();

        foreach ($brewingsToUpdate as $brewing) {
            $brewing->status = ActiveBrewing::STATUS_COMPLETED;
            $brewing->save();

            \Log::info('Автоматическое обновление статуса варки на "завершено" при загрузке страницы', [
                'brewing_id' => $brewing->id,
                'user_id' => $brewing->user_id,
                'recipe_id' => $brewing->recipe_id,
                'finished_at' => $brewing->finished_at->format('Y-m-d H:i:s'),
                'current_time' => Carbon::now()->format('Y-m-d H:i:s')
            ]);
        }

        $activeBrewingModel = ActiveBrewing::getActiveBrewingForUser($user->id); // Получаем активную варку пользователя
        $activeBrewing = null; // Инициализируем переменную для активной варки // Initialize variable for active brewing

        if ($activeBrewingModel) { // Если есть активная варка // If there is active brewing
            // Получаем связанный рецепт // Get related recipe
            $recipe = $activeBrewingModel->recipe;

            // Проверяем, завершена ли варка // Check if brewing is completed
            $completed = $activeBrewingModel->isCompleted();
            // Получаем оставшееся время и прогресс // Get remaining time and progress
            $timeLeft = $activeBrewingModel->getRemainingTimeInSeconds();
            $progress = $activeBrewingModel->getProgressPercentage();

            // Определяем иконку для зелья
            $potionIcon = 'potions/smallBottleHP.png'; // Иконка по умолчанию

            // Сначала ищем существующий шаблон зелья для этого рецепта
            $potionTemplate = Potion::where('recipe_id', $recipe->id)
                ->where('is_template', true)
                ->where('user_id', null)
                ->first();

            if ($potionTemplate && !empty($potionTemplate->icon)) {
                // Если нашли шаблон зелья, используем его иконку
                $potionIcon = $potionTemplate->icon;

                // Если путь не содержит 'potions/', добавляем его
                if (strpos($potionIcon, 'potions/') === false) {
                    $potionIcon = 'potions/' . basename($potionIcon);
                }

                \Log::info('Используем иконку из существующего шаблона зелья', [
                    'recipe_id' => $recipe->id,
                    'template_id' => $potionTemplate->id,
                    'template_icon' => $potionTemplate->icon,
                    'potion_icon' => $potionIcon
                ]);
            }
            // Если шаблон не найден, пробуем преобразовать иконку рецепта
            else if (!empty($recipe->icon)) {
                // Получаем только имя файла из пути рецепта
                $iconFileName = basename($recipe->icon);

                // Проверяем, содержит ли путь к иконке рецепта "Recipe" или "recipe"
                if (stripos($recipe->icon, 'recipe') !== false) {
                    // Если это иконка рецепта, заменяем на соответствующую иконку зелья
                    // Например, regularRecipe.png -> regularPotion.png
                    $potionIcon = str_ireplace('recipe', 'potion', $iconFileName);
                    // Добавляем путь к папке с иконками зелий
                    $potionIcon = 'potions/' . $potionIcon;
                } else {
                    // Если путь содержит 'icons/recipes/', заменяем на 'potions/'
                    if (stripos($recipe->icon, 'icons/recipes/') !== false) {
                        // Получаем имя файла и заменяем "Recipe" на "Potion" если есть
                        $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                        // Добавляем путь к папке с иконками зелий
                        $potionIcon = 'potions/' . $potionIcon;
                    } else {
                        // Проверяем, есть ли в имени файла слово "Recipe"
                        if (stripos($iconFileName, 'Recipe') !== false) {
                            // Заменяем "Recipe" на "Potion"
                            $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                            // Добавляем путь к папке с иконками зелий
                            $potionIcon = 'potions/' . $potionIcon;
                        } else {
                            // Если это не иконка рецепта, используем стандартную иконку зелья
                            $potionIcon = 'potions/smallBottleHP.png';
                        }
                    }
                }
            }

            // Логируем информацию об иконке для отладки
            \Log::info('Определена иконка для зелья в процессе варки', [
                'recipe_id' => $recipe->id,
                'recipe_name' => $recipe->name,
                'recipe_icon' => $recipe->icon,
                'recipe_icon_path' => $recipe->icon_path,
                'potion_icon' => $potionIcon,
                'potion_icon_path' => asset('assets/' . $potionIcon)
            ]);

            // Формируем объект с данными об активной варке для представления // Create object with active brewing data for the view
            // Используем название и описание зелья, а не рецепта
            $potionName = $recipe->name;
            $potionDescription = $recipe->description;

            // Если есть шаблон зелья, используем его название и описание
            if ($potionTemplate) {
                $potionName = $potionTemplate->name;
                $potionDescription = $potionTemplate->description;
            }

            $activeBrewing = (object) [
                'id' => $activeBrewingModel->recipe_id, // ID рецепта // Recipe ID
                'potion_name' => $potionName, // Название зелья // Potion name
                'description' => $potionDescription, // Описание // Description
                'quality' => $activeBrewingModel->quality, // Качество // Quality
                'started_at' => $activeBrewingModel->started_at->toDateTimeString(), // Время начала в формате строки // Start time as string
                'finished_at' => $activeBrewingModel->finished_at->toDateTimeString(), // Время окончания в формате строки // End time as string
                'brewing_progress' => $progress, // Прогресс // Progress
                'time_left' => $timeLeft, // Оставшееся время в секундах // Time left in seconds
                'completed' => $completed, // Флаг завершения // Completion flag
                'icon' => $potionIcon, // Иконка (путь) // Icon (path)
                'icon_path' => asset('assets/' . $potionIcon), // Полный путь к иконке зелья
                'color' => $recipe->color ?? '#ff5555' // Цвет // Color
            ];

            // Если варка завершена, но статус еще не обновлен // If brewing is completed but status not updated yet
            if ($completed && $activeBrewingModel->status === ActiveBrewing::STATUS_BREWING) {
                // Обновляем статус варки на "завершено" // Update brewing status to "completed"
                $activeBrewingModel->complete();

                // Логгируем завершение // Log completion
                $this->addAlchemyLog($user->id, "Завершено приготовление: {$recipe->name} ({$activeBrewingModel->quality})", true);

                // Проверяем, есть ли уже запись в pending_potions для этой варки
                $existingPendingPotion = PendingPotion::where('brewing_id', $activeBrewingModel->id)
                    ->where('user_id', $user->id)
                    ->first();

                if (!$existingPendingPotion) {
                    // Создаем запись о готовом зелье в таблице pending_potions
                    try {
                        // Убедимся, что путь к иконке не содержит 'assets/' в начале
                        $iconPath = $potionIcon;
                        if (strpos($iconPath, 'assets/') === 0) {
                            $iconPath = substr($iconPath, 7); // Убираем 'assets/' из начала пути
                        }

                        // Логируем информацию об иконке перед созданием записи
                        \Log::info('Создание записи о готовом зелье в pending_potions при обновлении статуса варки', [
                            'original_icon' => $potionIcon,
                            'cleaned_icon' => $iconPath
                        ]);

                        PendingPotion::create([
                            'user_id' => $user->id,
                            'brewing_id' => $activeBrewingModel->id,
                            'recipe_id' => $recipe->id,
                            'name' => $potionName,
                            'description' => $potionDescription,
                            'icon' => $iconPath, // Используем очищенный путь
                            'color' => $recipe->color ?? '#ff5555',
                            'quality' => $activeBrewingModel->quality,
                            'effect' => $recipe->effect,
                            'effect_value' => $recipe->effect_value,
                            'effect_duration' => $recipe->effect_duration,
                            'level' => $recipe->level,
                            'completed_at' => now()
                        ]);

                        \Log::info('Создана запись о готовом зелье в таблице pending_potions при обновлении статуса варки', [
                            'user_id' => $user->id,
                            'brewing_id' => $activeBrewingModel->id,
                            'recipe_id' => $recipe->id
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Ошибка при создании записи о готовом зелье в таблице pending_potions при обновлении статуса варки', [
                            'user_id' => $user->id,
                            'brewing_id' => $activeBrewingModel->id,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            }
        }

        // Получаем завершенные, но не собранные варки // Get completed but not collected brewings
        $completedBrewings = ActiveBrewing::getCompletedBrewingsForUser($user->id);

        // --- Получение доступных рецептов --- // --- Get available recipes ---
        // Получаем все активные рецепты с их ингредиентами // Get all active recipes with their ingredients
        $recipes = PotionRecipe::where('is_active', true)->with('ingredients.resource')->get();

        // Загружаем рецепты из инвентаря пользователя
        // Loading recipes from user inventory
        $userRecipesCollection = $user->userRecipes()
            ->with('recipe')
            ->where('quantity', '>', 0)
            ->get();

        // Обрабатываем каждый рецепт для отображения // Process each recipe for display
        $availablePotions = $recipes->map(function ($recipe) use ($userProfile, $inventoryResources, $activeBrewing) {
            // Проверка минимального уровня алхимии // Check minimum alchemy level
            $canBrewLevel = ($userProfile->alchemy_level ?? 1) >= $recipe->min_alchemy_level;
            // Проверка наличия ингредиентов // Check ingredient availability
            $canBrewIngredients = true;
            $missingIngredientsInfo = []; // Информация о недостающих ингредиентах // Information about missing ingredients

            foreach ($recipe->ingredients as $ingredient) { // Перебираем ингредиенты рецепта // Iterate over recipe ingredients
                // Ищем ресурс в инвентаре пользователя по resource_id // Find resource in user inventory by resource_id
                // Получаем ID ресурса из ингредиента
                $resourceId = $ingredient->resource_id;
                // Ищем ресурс в инвентаре пользователя
                $userResource = $inventoryResources->first(function ($item) use ($resourceId) {
                    return $item->resource->id === $resourceId;
                });
                $userQuantity = $userResource ? $userResource->total_quantity : 0; // Количество у пользователя // User quantity
                $neededQuantity = $ingredient->pivot->quantity; // Требуемое количество // Required quantity

                // Добавляем информацию в объект ингредиента для шаблона // Add information to the ingredient object for the template
                $ingredient->user_quantity = $userQuantity;
                $ingredient->has_enough = $userQuantity >= $neededQuantity;
                $ingredient->required_quantity = $neededQuantity;
                $ingredient->icon_path = $ingredient->resource->icon_path ?? asset('assets/icons/resources/default.png'); // Путь к иконке ингредиента // Ingredient icon path

                if (!$ingredient->has_enough) { // Если ингредиента не хватает // If ingredient is not enough
                    $canBrewIngredients = false; // Нельзя варить // Cannot brew
                    $missingIngredientsInfo[] = $ingredient->name . ' (' . ($neededQuantity - $userQuantity) . ' шт.)'; // Добавляем в список недостающих // Add to missing list
                }
            }

            // Определяем, можно ли варить зелье // Determine if the potion can be brewed
            $canBrew = $canBrewLevel && $canBrewIngredients && !$activeBrewing; // Уровень + Ингредиенты + Нет активной варки // Level + Ingredients + No active brewing

            // Формируем сообщение о причине невозможности варки // Create message explaining why brewing is not possible
            $cantBrewReason = '';
            if (!$canBrewLevel) {
                $cantBrewReason = 'Требуется уровень алхимии: ' . $recipe->min_alchemy_level;
            } elseif (!$canBrewIngredients) {
                $cantBrewReason = 'Не хватает: ' . implode(', ', $missingIngredientsInfo);
            } elseif ($activeBrewing) {
                $cantBrewReason = 'Алхимический стол занят';
            }

            // Возвращаем объект рецепта с дополнительной информацией // Return recipe object with additional information
            return (object) [
                'id' => $recipe->id,
                'name' => $recipe->name,
                'description' => $recipe->description,
                'quality' => $recipe->quality, // Качество рецепта // Recipe quality
                'quality_class' => $recipe->quality_class, // CSS класс качества // Quality CSS class
                'icon' => $recipe->icon, // Иконка рецепта (путь) // Recipe icon (path)
                'icon_path' => $recipe->icon_path, // Полный путь к иконке рецепта // Full recipe icon path
                'level' => $recipe->level,
                'color' => $recipe->color,
                'effect' => $recipe->effect,
                'effect_value' => $recipe->effect_value,
                'effect_duration' => $recipe->effect_duration,
                'min_alchemy_level' => $recipe->min_alchemy_level, // Мин. уровень // Min. level
                'brewing_time' => $recipe->brewing_time, // Базовое время варки // Base brewing time
                'brewing_time_modifier' => $recipe->brewing_time_modifier, // Модификатор времени // Time modifier
                'brewing_time_formatted' => $recipe->brewing_time_formatted, // Форматированное время варки (уже учитывает модификатор) // Formatted brewing time (already considers modifier)
                'ingredients' => $recipe->ingredients, // Ингредиенты // Ingredients
                'can_brew' => $canBrew, // Можно ли варить // Can brew?
                'cant_brew_reason' => $cantBrewReason, // Причина невозможности варки // Reason why cannot brew
                'is_active' => $recipe->is_active
            ];
        })->where('is_active', true); // Показываем только активные рецепты // Show only active recipes

        // --- Получение завершенных зелий из базы данных --- // --- Get completed potions from database ---
        // Сначала проверяем наличие готовых зелий в таблице pending_potions
        $pendingPotions = PendingPotion::where('user_id', $user->id)->get();
        $completedPotions = [];

        // Получаем завершенные, но не собранные варки
        $completedBrewings = ActiveBrewing::getCompletedBrewingsForUser($user->id);

        // Логируем информацию о найденных завершенных варках
        \Log::info('Найдены завершенные варки', [
            'user_id' => $user->id,
            'completed_brewings_count' => $completedBrewings->count(),
            'pending_potions_count' => $pendingPotions->count()
        ]);

        // Обрабатываем завершенные варки и создаем записи в pending_potions, если их еще нет
        foreach ($completedBrewings as $brewing) {
            // Проверяем, есть ли уже запись в pending_potions для этой варки
            $existingPendingPotion = $pendingPotions->where('brewing_id', $brewing->id)->first();

            if (!$existingPendingPotion) {
                $recipe = $brewing->recipe;

                if (!$recipe) {
                    \Log::warning('Рецепт не найден для завершенной варки', [
                        'user_id' => $user->id,
                        'brewing_id' => $brewing->id
                    ]);
                    continue;
                }

                // Определяем иконку для готового зелья
                $potionIcon = 'potions/smallBottleHP.png'; // Иконка по умолчанию

                // Сначала ищем существующий шаблон зелья для этого рецепта
                $potionTemplate = Potion::where('recipe_id', $recipe->id)
                    ->where('is_template', true)
                    ->where('user_id', null)
                    ->first();

                if ($potionTemplate && !empty($potionTemplate->icon)) {
                    // Если нашли шаблон зелья, используем его иконку
                    $potionIcon = $potionTemplate->icon;

                    // Если путь не содержит 'potions/', добавляем его
                    if (strpos($potionIcon, 'potions/') === false) {
                        $potionIcon = 'potions/' . basename($potionIcon);
                    }
                }
                // Если шаблон не найден, пробуем преобразовать иконку рецепта
                else if (!empty($recipe->icon)) {
                    // Получаем только имя файла из пути рецепта
                    $iconFileName = basename($recipe->icon);

                    // Проверяем, содержит ли путь к иконке рецепта "Recipe" или "recipe"
                    if (stripos($recipe->icon, 'recipe') !== false) {
                        // Если это иконка рецепта, заменяем на соответствующую иконку зелья
                        $potionIcon = str_ireplace('recipe', 'potion', $iconFileName);
                        // Добавляем путь к папке с иконками зелий
                        $potionIcon = 'potions/' . $potionIcon;
                    } else {
                        // Если путь содержит 'icons/recipes/', заменяем на 'potions/'
                        if (stripos($recipe->icon, 'icons/recipes/') !== false) {
                            // Получаем имя файла и заменяем "Recipe" на "Potion" если есть
                            $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                            // Добавляем путь к папке с иконками зелий
                            $potionIcon = 'potions/' . $potionIcon;
                        } else {
                            // Проверяем, есть ли в имени файла слово "Recipe"
                            if (stripos($iconFileName, 'Recipe') !== false) {
                                // Заменяем "Recipe" на "Potion"
                                $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                                // Добавляем путь к папке с иконками зелий
                                $potionIcon = 'potions/' . $potionIcon;
                            } else {
                                // Если это не иконка рецепта, используем стандартную иконку зелья
                                $potionIcon = 'potions/smallBottleHP.png';
                            }
                        }
                    }
                }

                try {
                    // Создаем запись о готовом зелье в таблице pending_potions
                    // Убедимся, что путь к иконке не содержит 'assets/' в начале
                    $iconPath = $potionIcon;
                    if (strpos($iconPath, 'assets/') === 0) {
                        $iconPath = substr($iconPath, 7); // Убираем 'assets/' из начала пути
                    }

                    // Логируем информацию об иконке перед созданием записи
                    \Log::info('Создание записи о готовом зелье в pending_potions', [
                        'original_icon' => $potionIcon,
                        'cleaned_icon' => $iconPath
                    ]);

                    $pendingPotion = PendingPotion::create([
                        'user_id' => $user->id,
                        'brewing_id' => $brewing->id,
                        'recipe_id' => $recipe->id,
                        'name' => $recipe->name,
                        'description' => $recipe->description,
                        'icon' => $iconPath, // Используем очищенный путь
                        'color' => $recipe->color,
                        'quality' => $brewing->quality,
                        'effect' => $recipe->effect,
                        'effect_value' => $recipe->effect_value,
                        'effect_duration' => $recipe->effect_duration,
                        'level' => $recipe->level,
                        'completed_at' => $brewing->finished_at
                    ]);

                    \Log::info('Создана новая запись в pending_potions для завершенной варки', [
                        'user_id' => $user->id,
                        'brewing_id' => $brewing->id,
                        'pending_potion_id' => $pendingPotion->id
                    ]);

                    // Добавляем созданную запись в коллекцию pending_potions
                    $pendingPotions->push($pendingPotion);
                } catch (\Exception $e) {
                    \Log::error('Ошибка при создании записи о готовом зелье в таблице pending_potions', [
                        'user_id' => $user->id,
                        'brewing_id' => $brewing->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        }

        // Формируем массив готовых зелий для отображения
        foreach ($pendingPotions as $pendingPotion) {
            // Получаем путь к иконке через аксессор
            $iconPath = $pendingPotion->icon_path;

            // Логируем информацию об иконке для отладки
            \Log::info('Формирование объекта готового зелья для отображения', [
                'pending_potion_id' => $pendingPotion->id,
                'brewing_id' => $pendingPotion->brewing_id,
                'icon' => $pendingPotion->icon,
                'icon_path_from_accessor' => $iconPath
            ]);

            $completedPotions[$pendingPotion->brewing_id] = (object) [
                'unique_id' => $pendingPotion->brewing_id,
                'recipe_id' => $pendingPotion->recipe_id,
                'name' => $pendingPotion->name,
                'description' => $pendingPotion->description,
                'icon' => $pendingPotion->icon,
                'color' => $pendingPotion->color,
                'quality' => $pendingPotion->quality,
                'effect' => $pendingPotion->effect,
                'effect_value' => $pendingPotion->effect_value,
                'effect_duration' => $pendingPotion->effect_duration,
                'level' => $pendingPotion->level,
                'icon_path' => $iconPath, // Используем полученный путь
                'completed_at' => $pendingPotion->completed_at
            ];
        }

        \Log::info('Сформирован массив готовых зелий для отображения', [
            'user_id' => $user->id,
            'completed_potions_count' => count($completedPotions),
            'first_potion' => count($completedPotions) > 0 ? reset($completedPotions) : null
        ]);

        // --- Получение логов алхимии из базы данных --- // --- Get alchemy logs from database ---
        $alchemyLogsRaw = AlchemyLog::getLatestLogsForUser($user->id); // Получаем последние 20 логов // Get last 20 logs
        $alchemyLogs = $alchemyLogsRaw->map(function ($log) {
            return (object) [
                'message' => $log->message,
                'success' => $log->success,
                'created_at' => $log->created_at // Используем атрибут с автоматическим форматированием
            ];
        })->toArray();

        // --- Хлебные крошки --- // --- Breadcrumbs ---
        $breadcrumbs = [
            ['name' => 'Главная', 'url' => route('home')],
            ['name' => 'Мастера', 'url' => route('masters.index')],
            ['name' => 'Алхимик'], // Текущая страница // Current page
        ];

        // --- Другие данные для представления --- // --- Other data for the view ---
        $onlineCount = User::where('last_activity_timestamp', '>=', now()->subMinutes(15)->timestamp)->count();
        $alchemyLevel = $userProfile->alchemy_level ?? 1; // Уровень алхимии // Alchemy level
        $experience = $userProfile->alchemy_experience ?? 0; // Опыт алхимии // Alchemy experience
        // Предполагаем, что есть модель LevelThreshold для получения опыта для следующего уровня // Assume LevelThreshold model exists for next level experience
        $nextLevelExperience = LevelThreshold::where('level', $alchemyLevel)->value('experience_threshold') ?? ($alchemyLevel * 100); // Опыт для след. уровня // Exp for next level
        $experienceProgressPercentage = $nextLevelExperience > 0 ? min(100, ($experience / $nextLevelExperience) * 100) : 0; // Прогресс опыта // Experience progress

        // Получаем актуальные ресурсы HP/MP для компонентов
        $actualResources = $userProfile->getActualResources();

        // Проверяем наличие непрочитанных сообщений через переписки
        $unreadMessagesCount = $user->conversations()
            ->sum('conversation_user.unread_count');
        $hasUnreadMessages = $unreadMessagesCount > 0;

        // Проверяем наличие сломанных предметов
        $brokenItems = \App\Models\GameItem::where('owner_id', $user->id)
            ->where('durability', 0)
            ->get();
        $hasBrokenItems = $brokenItems->count() > 0;
        $brokenItemsCount = $brokenItems->count();

        // Получаем катализаторы пользователя
        $userCatalysts = UserCatalyst::where('user_id', $user->id)
            ->where('quantity', '>', 0)
            ->with('catalyst')
            ->get()
            ->map(function ($userCatalyst) {
                return [
                    'id' => $userCatalyst->catalyst->id,
                    'name' => $userCatalyst->catalyst->name,
                    'description' => $userCatalyst->catalyst->description,
                    'quality_bonus' => $userCatalyst->catalyst->quality_bonus,
                    'rarity' => $userCatalyst->catalyst->rarity,
                    'rarity_name' => $userCatalyst->catalyst->getRarityNameAttribute(),
                    'icon' => $userCatalyst->catalyst->icon,
                    'image_path' => $userCatalyst->catalyst->image_path,
                    'quantity' => $userCatalyst->quantity
                ];
            });

        // Возвращаем представление со всеми данными // Return the view with all data
        return view('masters.alchemist', [
            'userProfile' => $userProfile,
            'activeBrewing' => $activeBrewing,
            'brewingProgress' => $activeBrewing ? $activeBrewing->brewing_progress : 0,
            'timeLeftFormatted' => $activeBrewing ? $this->formatTimeLeft($activeBrewing->time_left) : '0:00', // Форматированное время // Formatted time
            'availablePotions' => $availablePotions,
            'inventoryResources' => $inventoryResources,
            'completedPotions' => $completedPotions,
            'userPotions' => UserPotion::where('user_id', $user->id)->get(), // Зелья пользователя из новой таблицы
            'oldUserPotions' => Potion::where('user_id', $user->id)->get(), // Старые зелья пользователя (для обратной совместимости)
            'userCatalysts' => $userCatalysts, // Катализаторы пользователя
            'alchemyLogs' => $alchemyLogs,
            'alchemyLevel' => $alchemyLevel,
            'breadcrumbs' => $breadcrumbs,
            'onlineCount' => $onlineCount,
            'userRecipes' => $userRecipesCollection, // Добавляем рецепты пользователя в шаблон
            'userRecipesCollection' => $userRecipesCollection, // Дублируем для новой функциональности
            'experienceProgress' => [
                'current_experience' => $experience,
                'next_experience' => $nextLevelExperience,
                'percentage' => $experienceProgressPercentage
            ],
            'actualResources' => $actualResources,
            'hasUnreadMessages' => $hasUnreadMessages,
            'unreadMessagesCount' => $unreadMessagesCount,
            'hasBrokenItems' => $hasBrokenItems,
            'brokenItemsCount' => $brokenItemsCount
        ]);
    }

    /**
     * Форматирует оставшееся время в секундах в строку формата М:СС
     * Formats remaining time in seconds into an M:SS formatted string.
     *
     * @param int $seconds Время в секундах // Time in seconds
     * @return string Форматированное время // Formatted time
     */
    private function formatTimeLeft($seconds)
    {
        if ($seconds < 0)
            $seconds = 0; // Не допускаем отрицательное время // Prevent negative time
        $minutes = floor($seconds / 60); // Вычисляем целое количество минут // Calculate whole minutes
        $remainingSeconds = $seconds % 60; // Вычисляем оставшиеся секунды // Calculate remaining seconds
        // Возвращаем строку в формате M:SS, добавляя ведущий ноль к секундам, если нужно // Return string in M:SS format, adding leading zero to seconds if needed
        return $minutes . ':' . str_pad($remainingSeconds, 2, '0', STR_PAD_LEFT);
    }

    /**
     * Форматирует время варки в удобочитаемый формат
     * Formats brewing time in a human-readable format.
     *
     * @param int $seconds Количество секунд // Number of seconds
     * @return string Форматированное время // Formatted time
     */
    private function formatBrewingTime($seconds)
    {
        if ($seconds < 60) {
            return $seconds . ' сек.';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . ' мин.' . ($remainingSeconds > 0 ? ' ' . $remainingSeconds . ' сек.' : '');
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . ' ч.' . ($minutes > 0 ? ' ' . $minutes . ' мин.' : '');
        }
    }

    /**
     * Начинает процесс варки зелья
     * Starts the potion brewing process.
     *
     * @param Request $request Запрос, содержащий 'recipe_id' // Request containing 'recipe_id'
     * @return \Illuminate\Http\JsonResponse Результат операции в формате JSON // Operation result in JSON format
     */
    public function startBrewing(Request $request)
    {
        // Проверка авторизации пользователя // Check user authorization
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
        }

        $user = Auth::user(); // Текущий пользователь // Current user
        $userProfile = $user->profile; // Получаем профиль пользователя // Get user profile

        // Проверка, не занят ли уже алхимический стол (наличие активной варки в базе данных)
        // Check if the alchemy table is already occupied (existence of active brewing in database)
        $activeBrewing = ActiveBrewing::getActiveBrewingForUser($user->id);
        if ($activeBrewing) {
            return response()->json(['success' => false, 'message' => 'Алхимический стол уже занят']); // Ошибка, если стол занят // Error if table is occupied
        }

        // Получаем ID рецепта и катализатора из входящего запроса // Get recipe ID and catalyst ID from the incoming request
        $recipeId = $request->input('recipe_id');
        $catalystId = $request->input('catalyst_id'); // Получаем ID катализатора (если есть)

        // Проверяем, что ID рецепта предоставлен
        if (!$recipeId) {
            return response()->json([
                'success' => false,
                'message' => 'ID рецепта не предоставлен'
            ]);
        }

        // Сначала проверяем, есть ли такой рецепт у пользователя в инвентаре
        $userRecipe = $user->userRecipes()
            ->where('potion_recipe_id', $recipeId)
            ->where('quantity', '>', 0)
            ->first();

        if (!$userRecipe) {
            $this->addAlchemyLog($user->id, "Попытка сварить зелье по отсутствующему в инвентаре рецепту ID: {$recipeId}", false);
            return response()->json([
                'success' => false,
                'message' => 'Рецепт не найден в вашем инвентаре'
            ]);
        }

        // Ищем рецепт в базе данных, сразу загружая связанные ингредиенты и их ресурсы // Find the recipe in the database, eagerly loading related ingredients and their resources
        $recipe = PotionRecipe::with('ingredients.resource')->find($recipeId);

        // Проверка, найден ли рецепт и активен ли он // Check if the recipe was found and is active
        if (!$recipe) {
            // Логгируем попытку использовать неверный рецепт // Log attempt to use invalid recipe
            $this->addAlchemyLog($user->id, "Попытка сварить зелье по несуществующему рецепту ID: {$recipeId}", false);
            return response()->json(['success' => false, 'message' => 'Рецепт не найден']); // Ошибка, если рецепт не найден // Error if recipe not found
        }

        if (!$recipe->is_active) {
            // Логгируем попытку использовать неактивный рецепт // Log attempt to use inactive recipe
            $this->addAlchemyLog($user->id, "Попытка сварить зелье по неактивному рецепту ID: {$recipeId} ({$recipe->name})", false);
            return response()->json(['success' => false, 'message' => 'Этот рецепт неактивен']); // Ошибка, если рецепт неактивен // Error if recipe inactive
        }

        // Проверка уровня алхимии пользователя // Check user's alchemy level
        $alchemyLevel = $userProfile->alchemy_level ?? 1; // Получаем уровень алхимии // Get alchemy level
        if ($alchemyLevel < $recipe->min_alchemy_level) {
            // Логгируем попытку варки с недостаточным уровнем // Log brewing attempt with insufficient level
            $this->addAlchemyLog($user->id, "Попытка сварить {$recipe->name}: недостаточный уровень алхимии ({$alchemyLevel} < {$recipe->min_alchemy_level})", false);
            return response()->json(['success' => false, 'message' => "Требуется уровень алхимии: {$recipe->min_alchemy_level}"]); // Ошибка, если уровень недостаточен // Error if level is insufficient
        }

        // Начинаем транзакцию базы данных для атомарного списания ингредиентов // Start database transaction for atomic ingredient consumption
        DB::beginTransaction();
        try {
            $missingIngredients = []; // Массив для хранения недостающих ингредиентов // Array to store missing ingredients
            // Перебираем все необходимые ингредиенты для рецепта // Iterate over all required ingredients for the recipe
            foreach ($recipe->ingredients as $ingredient) {
                $needed = $ingredient->pivot->quantity; // Требуемое количество текущего ингредиента // Required quantity of the current ingredient

                // Получаем ID ингредиента
                $ingredientId = $ingredient->id;

                // Проверяем наличие ингредиента напрямую в таблице user_alchemy_ingredients
                $userAlchemyIngredient = UserAlchemyIngredient::where('user_id', $user->id)
                    ->where('alchemy_ingredient_id', $ingredientId)
                    ->where('location', 'inventory')
                    ->where('quantity', '>', 0)
                    ->sum('quantity');

                // Если ингредиент есть напрямую в таблице user_alchemy_ingredients
                if ($userAlchemyIngredient >= $needed) {
                    // Списываем ингредиент напрямую из user_alchemy_ingredients
                    $consumeResult = $this->consumeAlchemyIngredient($user->id, $ingredientId, $needed);

                    // Проверяем результат списания
                    if (!$consumeResult['success']) {
                        // Добавляем информацию о недостающем ингредиенте в массив
                        // consumeResult['needed'] содержит количество, которого не хватает
                        $missingIngredients[] = $ingredient->name . ' (' . $consumeResult['needed'] . ' шт.)';
                    } else {
                        continue; // Переходим к следующему ингредиенту только если списание успешно
                    }
                }

                // Если у ингредиента нет resource_id, но при этом его нет и в user_alchemy_ingredients
                if (!$ingredient->resource_id) {
                    // Логгируем ошибку
                    \Log::error('Ошибка: у ингредиента отсутствует resource_id и его нет в инвентаре', [
                        'ingredient_id' => $ingredient->id,
                        'ingredient_name' => $ingredient->name,
                        'recipe_id' => $recipe->id,
                        'recipe_name' => $recipe->name
                    ]);

                    // Добавляем информацию о недостающем ингредиенте в массив
                    // Здесь $needed - это общее требуемое количество ингредиента
                    $missingIngredients[] = $ingredient->name . ' (' . $needed . ' шт.)';
                    continue; // Пропускаем этот ингредиент и переходим к следующему
                }

                // Пытаемся списать необходимое количество ресурса из инвентаря пользователя
                $consumeResult = $this->consumeResource($user->id, $ingredient->resource_id, $needed);

                // Если списание ресурса не удалось (не хватило) // If resource consumption failed (insufficient quantity)
                if (!$consumeResult['success']) {
                    // Добавляем информацию о недостающем ингредиенте в массив // Add information about the missing ingredient to the array
                    // consumeResult['needed'] содержит количество, которого не хватает
                    $missingIngredients[] = $ingredient->resource->name . ' (' . $consumeResult['needed'] . ' шт.)'; // Используем имя ресурса // Use resource name
                }
            }

            // Если массив недостающих ингредиентов не пуст // If the array of missing ingredients is not empty
            if (!empty($missingIngredients)) {
                DB::rollBack(); // Откатываем транзакцию, так как ингредиентов не хватило // Rollback the transaction as ingredients were insufficient
                $message = 'Не хватает ингредиентов: ' . implode(', ', $missingIngredients); // Формируем сообщение об ошибке // Create error message
                // Логгируем неудачную попытку варки из-за нехватки ингредиентов // Log failed brewing attempt due to lack of ingredients
                $this->addAlchemyLog($user->id, "Попытка сварить {$recipe->name}: {$message}", false);
                return response()->json(['success' => false, 'message' => $message]); // Возвращаем ошибку // Return error
            }

            // Проверяем катализатор, если он указан
            $catalyst = null;
            $catalystUsed = null;
            $potionQuality = $recipe->quality; // По умолчанию качество из рецепта

            if ($catalystId) {
                // Проверяем, существует ли катализатор
                $catalyst = AlchemyCatalyst::find($catalystId);

                if (!$catalyst) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'Указанный катализатор не найден'
                    ]);
                }

                // Проверяем, есть ли у пользователя этот катализатор
                $userCatalyst = UserCatalyst::where('user_id', $user->id)
                    ->where('alchemy_catalyst_id', $catalyst->id)
                    ->where('quantity', '>', 0)
                    ->first();

                if (!$userCatalyst) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'message' => 'У вас нет этого катализатора'
                    ]);
                }

                // Определяем, улучшится ли качество зелья с помощью катализатора
                $qualityLevels = array_keys(Potion::QUALITY_LEVELS);
                $currentQualityIndex = array_search($potionQuality, $qualityLevels);

                // Если текущее качество не максимальное, пытаемся улучшить
                if ($currentQualityIndex !== false && $currentQualityIndex < count($qualityLevels) - 1) {
                    // Определяем шанс успеха в зависимости от редкости катализатора
                    $successChance = match ($catalyst->rarity) {
                        1 => 50,     // Обычный катализатор - 50% шанс
                        2 => 70,     // Необычный катализатор - 70% шанс
                        3 => 85,     // Редкий катализатор - 85% шанс
                        4 => 95,     // Эпический катализатор - 95% шанс
                        5 => 100,    // Легендарный катализатор - 100% шанс
                        default => 50 // По умолчанию 50%
                    };

                    // Определяем успех улучшения
                    $isSuccess = rand(1, 100) <= $successChance;

                    if ($isSuccess) {
                        // Улучшаем качество зелья
                        $potionQuality = $qualityLevels[$currentQualityIndex + 1];
                        $catalystUsed = $catalyst->id;
                    }
                }

                // Расходуем катализатор
                UserCatalyst::removeFromUser($user->id, $catalyst->id);
            }

            // Списываем рецепт из инвентаря пользователя
            if ($userRecipe->quantity > 1) {
                // Если рецептов больше одного, уменьшаем количество
                $userRecipe->decrement('quantity');
                \Log::info('Уменьшено количество рецептов в инвентаре', [
                    'user_id' => $user->id,
                    'recipe_id' => $recipeId,
                    'old_quantity' => $userRecipe->quantity + 1,
                    'new_quantity' => $userRecipe->quantity
                ]);
            } else {
                // Если это последний рецепт, удаляем запись и уменьшаем счетчик занятых слотов
                $userProfile = UserProfile::where('user_id', $user->id)->first();
                if ($userProfile && $userProfile->inventory_used > 0) {
                    $userProfile->decrement('inventory_used');
                    \Log::info('Уменьшен счетчик занятых слотов инвентаря', [
                        'user_id' => $user->id,
                        'old_inventory_used' => $userProfile->inventory_used + 1,
                        'new_inventory_used' => $userProfile->inventory_used
                    ]);
                }
                $userRecipe->delete();
                \Log::info('Удален последний рецепт из инвентаря', [
                    'user_id' => $user->id,
                    'recipe_id' => $recipeId
                ]);
            }

            // Если все ингредиенты успешно списаны и рецепт списан, подтверждаем транзакцию // If all ingredients were consumed successfully and recipe was consumed, commit the transaction
            DB::commit();

            // --- Начало процесса варки в базе данных --- // --- Start brewing process in database ---
            // Рассчитываем реальное время варки с учетом модификатора рецепта // Calculate actual brewing time considering the recipe modifier
            $actualBrewingTime = ceil(($recipe->brewing_time ?? 0) * ($recipe->brewing_time_modifier ?? 1));
            // Получаем уровень рецепта как целое число // Get recipe level as an integer
            $levelInt = intval($recipe->level);
            // Рассчитываем уменьшение времени варки в зависимости от уровня рецепта (в минутах) // Calculate brewing time reduction based on recipe level (in minutes)
            $brewingTimeReductionMinutes = max(30, 120 - ($levelInt - 1) * 15); // Минимум 30 минут // Minimum 30 minutes
            // Применяем уменьшение времени, конвертируя минуты в секунды // Apply time reduction, converting minutes to seconds
            $finalBrewingTimeSeconds = min($actualBrewingTime, $brewingTimeReductionMinutes * 60); // Окончательное время варки // Final brewing time

            // Рассчитываем множители для силы и длительности эффекта на основе уровня рецепта
            // Calculate multipliers for effect strength and duration based on recipe level
            // Эти значения можно использовать в будущем для улучшения зелий
            // These values can be used in the future for potion enhancement
            // $effectMultiplier = 1 + ($levelInt - 1) * 0.1667; // Множитель эффекта (от 1x до ~2x)
            // $durationMultiplier = 1 + ($levelInt - 1) * 0.25; // Множитель длительности (от 1x до 2.5x)
            // $adjustedEffectValue = round(($recipe->effect_value ?? 0) * $effectMultiplier);
            // $adjustedDuration = round(($recipe->effect_duration ?? 0) * $durationMultiplier);

            // Собираем информацию об использованных ингредиентах для сохранения в JSON
            $ingredientsUsed = [];
            foreach ($recipe->ingredients as $ingredient) {
                $ingredientsUsed[] = [
                    'id' => $ingredient->id,
                    'name' => $ingredient->name,
                    'quantity' => $ingredient->pivot->quantity
                ];
            }

            // Создаем запись о варке в базе данных
            $activeBrewing = ActiveBrewing::create([
                'user_id' => $user->id,
                'recipe_id' => $recipe->id,
                'started_at' => now(),
                'finished_at' => now()->addSeconds($finalBrewingTimeSeconds),
                'status' => ActiveBrewing::STATUS_BREWING,
                'ingredients_used' => $ingredientsUsed,
                'catalyst_used' => $catalystUsed,
                'quality' => $potionQuality,
            ]);

            // Формируем сообщение для лога
            $logMessage = "Начато приготовление: {$recipe->name} ({$potionQuality}), рецепт использован";
            if ($catalystUsed) {
                $logMessage .= " с использованием катализатора: {$catalyst->name}";
                if ($potionQuality !== $recipe->quality) {
                    $logMessage .= " (улучшено до качества: {$potionQuality})";
                }
            }

            // Логгируем начало варки // Log brewing start
            $this->addAlchemyLog($user->id, $logMessage, true);

            // Возвращаем успешный ответ // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Начато приготовление зелья!',
                'brewing' => [
                    'id' => $activeBrewing->id,
                    'recipe_name' => $recipe->name,
                    'started_at' => $activeBrewing->started_at->format('Y-m-d H:i:s'),
                    'finished_at' => $activeBrewing->finished_at->format('Y-m-d H:i:s'),
                    'brewing_time' => $finalBrewingTimeSeconds,
                    'quality' => $potionQuality,
                    'catalyst_used' => $catalystUsed ? $catalyst->name : null
                ]
            ]);

        } catch (\Exception $e) {
            // В случае любой ошибки в блоке try // In case of any error in the try block
            DB::rollBack(); // Откат транзакции // Rollback the transaction
            // Логгируем ошибку // Log error
            \Log::error('Ошибка при начале варки зелья: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'recipe_id' => $recipeId,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            // Сформируем понятное сообщение об ошибке для пользователя
            $errorMessage = 'Произошла ошибка при начале варки.';

            if (strpos($e->getMessage(), "Attempt to read property") !== false) {
                $errorMessage = 'Ошибка доступа к данным рецепта. Возможно, рецепт поврежден или у ингредиента отсутствует связанный ресурс.';
            } elseif (strpos($e->getMessage(), "resource_id") !== false) {
                $errorMessage = 'Ошибка в данных ингредиента: отсутствует связанный ресурс.';
            }

            // Добавляем запись об ошибке в лог алхимии // Add error entry to alchemy log
            $recipeName = $recipe ? $recipe->name : "ID: {$recipeId}";
            $this->addAlchemyLog($user->id, "Ошибка при начале варки {$recipeName}: {$errorMessage}", false);

            // Возвращаем JSON ответ об ошибке // Return JSON error response
            return response()->json([
                'success' => false,
                'message' => $errorMessage,
                'details' => config('app.debug') ? $e->getMessage() : null
            ]);
        }
    }

    /**
     * Проверяет статус текущей варки зелья
     * Checks the status of the current potion brewing.
     *
     * Метод не принимает параметров, так как проверяет статус варки для текущего авторизованного пользователя
     * @return \Illuminate\Http\JsonResponse JSON ответ со статусом варки // JSON response with brewing status
     */
    public function checkBrewingStatus()
    {
        // Проверка авторизации // Authorization check
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
        }
        $user = Auth::user(); // Текущий пользователь // Current user

        // Получаем активную варку из базы данных // Get active brewing from database
        $activeBrewing = ActiveBrewing::getActiveBrewingForUser($user->id);

        // Если нет активной варки // If no active brewing
        if (!$activeBrewing) {
            // Проверяем, есть ли завершенные зелья для сбора // Check for completed potions to collect
            $completedBrewings = ActiveBrewing::getCompletedBrewingsForUser($user->id);
            $completedCount = $completedBrewings->count();

            // Возвращаем ответ, что варки нет, и количество готовых зелий // Return response indicating no brewing and the count of ready potions
            return response()->json([
                'success' => true,
                'brewing' => false, // Флаг: варка не идет // Flag: brewing is not in progress
                'completed_count' => $completedCount, // Количество готовых зелий // Number of completed potions
                'message' => $completedCount > 0 ? 'Есть готовые зелья для сбора.' : 'Нет активной варки.' // Сообщение // Message
            ]);
        }

        // Получаем связанный рецепт // Get related recipe
        $recipe = $activeBrewing->recipe;

        // Проверяем, завершена ли варка // Check if brewing is completed
        $completed = $activeBrewing->isCompleted();
        // Получаем оставшееся время и прогресс // Get remaining time and progress
        $timeLeft = $activeBrewing->getRemainingTimeInSeconds();
        $progress = $activeBrewing->getProgressPercentage();

        // Если варка завершилась только что (при этой проверке) или статус уже обновлен моделью
        // If brewing just completed (during this check) or status already updated by model
        if ($completed) {
            // Если статус еще не обновлен, обновляем его
            if ($activeBrewing->status === ActiveBrewing::STATUS_BREWING) {
                // Обновляем статус варки на "завершено" // Update brewing status to "completed"
                $activeBrewing->complete();

                // Логируем обновление статуса
                \Log::info('Обновление статуса варки на "завершено" в checkBrewingStatus', [
                    'user_id' => $user->id,
                    'brewing_id' => $activeBrewing->id,
                    'old_status' => ActiveBrewing::STATUS_BREWING,
                    'new_status' => ActiveBrewing::STATUS_COMPLETED
                ]);
            } else {
                // Логируем, что статус уже был обновлен
                \Log::info('Статус варки уже обновлен на "завершено"', [
                    'user_id' => $user->id,
                    'brewing_id' => $activeBrewing->id,
                    'status' => $activeBrewing->status
                ]);
            }

            // Определяем название зелья для лога
            $potionName = $recipe->name;

            // Ищем шаблон зелья для этого рецепта
            $potionTemplate = Potion::where('recipe_id', $recipe->id)
                ->where('is_template', true)
                ->where('user_id', null)
                ->first();

            // Если есть шаблон зелья, используем его название
            if ($potionTemplate && !empty($potionTemplate->name)) {
                $potionName = $potionTemplate->name;
            }

            // Логгируем завершение // Log completion
            $this->addAlchemyLog($user->id, "Завершено приготовление: {$potionName} ({$activeBrewing->quality})", true);

            // Получаем обновленное количество завершенных варок // Get updated count of completed brewings
            $completedCount = ActiveBrewing::getCompletedBrewingsForUser($user->id)->count();

            // Определяем иконку для готового зелья
            $potionIcon = 'potions/smallBottleHP.png'; // Иконка по умолчанию

            // Сначала ищем существующий шаблон зелья для этого рецепта
            $potionTemplate = Potion::where('recipe_id', $recipe->id)
                ->where('is_template', true)
                ->where('user_id', null)
                ->first();

            if ($potionTemplate && !empty($potionTemplate->icon)) {
                // Если нашли шаблон зелья, используем его иконку
                $potionIcon = $potionTemplate->icon;

                // Если путь не содержит 'potions/', добавляем его
                if (strpos($potionIcon, 'potions/') === false) {
                    $potionIcon = 'potions/' . basename($potionIcon);
                }

                \Log::info('Используем иконку из существующего шаблона зелья для готового зелья', [
                    'recipe_id' => $recipe->id,
                    'template_id' => $potionTemplate->id,
                    'template_icon' => $potionTemplate->icon,
                    'potion_icon' => $potionIcon
                ]);
            }
            // Если шаблон не найден, пробуем преобразовать иконку рецепта
            else if (!empty($recipe->icon)) {
                // Получаем только имя файла из пути рецепта
                $iconFileName = basename($recipe->icon);

                // Проверяем, содержит ли путь к иконке рецепта "Recipe" или "recipe"
                if (stripos($recipe->icon, 'recipe') !== false) {
                    // Если это иконка рецепта, заменяем на соответствующую иконку зелья
                    // Например, regularRecipe.png -> regularPotion.png
                    $potionIcon = str_ireplace('recipe', 'potion', $iconFileName);
                    // Добавляем путь к папке с иконками зелий
                    $potionIcon = 'potions/' . $potionIcon;
                } else {
                    // Если путь содержит 'icons/recipes/', заменяем на 'potions/'
                    if (stripos($recipe->icon, 'icons/recipes/') !== false) {
                        // Получаем имя файла и заменяем "Recipe" на "Potion" если есть
                        $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                        // Добавляем путь к папке с иконками зелий
                        $potionIcon = 'potions/' . $potionIcon;
                    } else {
                        // Проверяем, есть ли в имени файла слово "Recipe"
                        if (stripos($iconFileName, 'Recipe') !== false) {
                            // Заменяем "Recipe" на "Potion"
                            $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                            // Добавляем путь к папке с иконками зелий
                            $potionIcon = 'potions/' . $potionIcon;
                        } else {
                            // Если это не иконка рецепта, используем стандартную иконку зелья
                            $potionIcon = 'potions/smallBottleHP.png';
                        }
                    }
                }
            }

            // Логируем информацию об иконке для отладки
            \Log::info('Определена иконка для готового зелья', [
                'recipe_id' => $recipe->id,
                'recipe_name' => $recipe->name,
                'recipe_icon' => $recipe->icon,
                'recipe_icon_path' => $recipe->icon_path,
                'potion_icon' => $potionIcon,
                'potion_icon_path' => asset('assets/' . $potionIcon)
            ]);

            // Определяем название зелья
            $potionName = $recipe->name;
            $potionDescription = $recipe->description;
            $potionEffect = $recipe->effect;
            $potionEffectValue = $recipe->effect_value;
            $potionEffectDuration = $recipe->effect_duration;
            $potionLevel = $recipe->level;
            $potionColor = $recipe->color;

            // Если есть шаблон зелья, используем его данные
            if ($potionTemplate) {
                if (!empty($potionTemplate->name)) {
                    $potionName = $potionTemplate->name;
                }
                if (!empty($potionTemplate->description)) {
                    $potionDescription = $potionTemplate->description;
                }
                if (!empty($potionTemplate->effect)) {
                    $potionEffect = $potionTemplate->effect;
                }
                if (!empty($potionTemplate->effect_value)) {
                    $potionEffectValue = $potionTemplate->effect_value;
                }
                if (!empty($potionTemplate->effect_duration)) {
                    $potionEffectDuration = $potionTemplate->effect_duration;
                }
                if (!empty($potionTemplate->level)) {
                    $potionLevel = $potionTemplate->level;
                }
                if (!empty($potionTemplate->color)) {
                    $potionColor = $potionTemplate->color;
                }
            }

            // Проверяем, существует ли уже запись о готовом зелье для этой варки
            $existingPendingPotion = PendingPotion::where('brewing_id', $activeBrewing->id)
                ->where('user_id', $user->id)
                ->first();

            // Если записи нет, создаем новую
            if (!$existingPendingPotion) {
                try {
                    // Создаем запись о готовом зелье в таблице pending_potions
                    $pendingPotion = PendingPotion::create([
                        'user_id' => $user->id,
                        'brewing_id' => $activeBrewing->id,
                        'recipe_id' => $recipe->id,
                        'name' => $potionName,
                        'description' => $potionDescription,
                        'icon' => $potionIcon,
                        'color' => $potionColor,
                        'quality' => $activeBrewing->quality,
                        'effect' => $potionEffect,
                        'effect_value' => $potionEffectValue,
                        'effect_duration' => $potionEffectDuration,
                        'level' => $potionLevel,
                        'completed_at' => now()
                    ]);

                    \Log::info('Создана запись о готовом зелье в таблице pending_potions', [
                        'user_id' => $user->id,
                        'brewing_id' => $activeBrewing->id,
                        'pending_potion_id' => $pendingPotion->id,
                        'potion_name' => $potionName,
                        'potion_icon' => $potionIcon
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Ошибка при создании записи о готовом зелье в таблице pending_potions', [
                        'user_id' => $user->id,
                        'brewing_id' => $activeBrewing->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                \Log::info('Запись о готовом зелье уже существует в таблице pending_potions', [
                    'user_id' => $user->id,
                    'brewing_id' => $activeBrewing->id,
                    'pending_potion_id' => $existingPendingPotion->id,
                    'potion_name' => $existingPendingPotion->name,
                    'potion_icon' => $existingPendingPotion->icon
                ]);
            }

            // Сообщаем фронтенду, что варка завершена и есть готовое зелье // Inform frontend that brewing is complete and potion is ready
            return response()->json([
                'success' => true,
                'brewing' => false, // Варка больше не активна // Brewing no longer active
                'completed' => true, // Флаг: завершено // Flag: completed
                'completed_count' => $completedCount, // Обновленное количество готовых // Updated count of ready potions
                'message' => 'Приготовление зелья завершено!', // Сообщение // Message
                'potion_name' => $potionName, // Название зелья // Potion name
                'brewing_id' => $activeBrewing->id, // ID варки для сбора зелья // Brewing ID for potion collection
                'potion_quality' => $activeBrewing->quality, // Качество зелья // Potion quality
                'potion_icon_path' => asset('assets/' . $potionIcon), // Полный путь к иконке зелья
                'time_left' => 0, // Оставшееся время (0, так как варка завершена)
                'time_left_formatted' => '0:00', // Форматированное время (0:00, так как варка завершена)
                'brewing_progress' => 100 // Прогресс варки (100%, так как варка завершена)
            ]);
        }

        // Если варка еще идет // If brewing is still in progress
        // Определяем иконку для зелья в процессе варки
        $potionIcon = 'potions/smallBottleHP.png'; // Иконка по умолчанию

        // Сначала ищем существующий шаблон зелья для этого рецепта
        $potionTemplate = Potion::where('recipe_id', $recipe->id)
            ->where('is_template', true)
            ->where('user_id', null)
            ->first();

        if ($potionTemplate && !empty($potionTemplate->icon)) {
            // Если нашли шаблон зелья, используем его иконку
            $potionIcon = $potionTemplate->icon;

            // Если путь не содержит 'potions/', добавляем его
            if (strpos($potionIcon, 'potions/') === false) {
                $potionIcon = 'potions/' . basename($potionIcon);
            }

            \Log::info('Используем иконку из существующего шаблона зелья для зелья в процессе варки', [
                'recipe_id' => $recipe->id,
                'template_id' => $potionTemplate->id,
                'template_icon' => $potionTemplate->icon,
                'potion_icon' => $potionIcon
            ]);
        }
        // Если шаблон не найден, пробуем преобразовать иконку рецепта
        else if (!empty($recipe->icon)) {
            // Получаем только имя файла из пути рецепта
            $iconFileName = basename($recipe->icon);

            // Проверяем, содержит ли путь к иконке рецепта "Recipe" или "recipe"
            if (stripos($recipe->icon, 'recipe') !== false) {
                // Если это иконка рецепта, заменяем на соответствующую иконку зелья
                // Например, regularRecipe.png -> regularPotion.png
                $potionIcon = str_ireplace('recipe', 'potion', $iconFileName);
                // Добавляем путь к папке с иконками зелий
                $potionIcon = 'potions/' . $potionIcon;
            } else {
                // Если путь содержит 'icons/recipes/', заменяем на 'potions/'
                if (stripos($recipe->icon, 'icons/recipes/') !== false) {
                    // Получаем имя файла и заменяем "Recipe" на "Potion" если есть
                    $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                    // Добавляем путь к папке с иконками зелий
                    $potionIcon = 'potions/' . $potionIcon;
                } else {
                    // Проверяем, есть ли в имени файла слово "Recipe"
                    if (stripos($iconFileName, 'Recipe') !== false) {
                        // Заменяем "Recipe" на "Potion"
                        $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                        // Добавляем путь к папке с иконками зелий
                        $potionIcon = 'potions/' . $potionIcon;
                    } else {
                        // Если это не иконка рецепта, используем стандартную иконку зелья
                        $potionIcon = 'potions/smallBottleHP.png';
                    }
                }
            }
        }

        // Логируем информацию об иконке для отладки
        \Log::info('Определена иконка для зелья в процессе варки', [
            'recipe_id' => $recipe->id,
            'recipe_name' => $recipe->name,
            'recipe_icon' => $recipe->icon,
            'recipe_icon_path' => $recipe->icon_path,
            'potion_icon' => $potionIcon,
            'potion_icon_path' => asset('assets/' . $potionIcon)
        ]);

        // Определяем название и описание зелья
        $potionName = $recipe->name;

        // Если есть шаблон зелья, используем его название
        if ($potionTemplate) {
            $potionName = $potionTemplate->name;
        }

        return response()->json([
            'success' => true,
            'brewing' => true, // Флаг: варка идет // Flag: brewing is in progress
            'completed' => false, // Флаг: не завершено // Flag: not completed
            'progress' => round($progress), // Округленный прогресс // Rounded progress
            'time_left' => $timeLeft, // Оставшееся время в секундах // Remaining time in seconds
            'time_left_formatted' => $this->formatTimeLeft($timeLeft), // Форматированное время М:СС // Formatted time M:SS
            'potion_name' => $potionName, // Название зелья // Potion name
            // Полный путь к иконке для отображения // Full icon path for display
            'potion_icon_path' => asset('assets/' . $potionIcon), // Полный путь к иконке зелья
            'potion_quality' => $activeBrewing->quality // Качество зелья // Potion quality
        ]);
    }

    /**
     * Собирает готовое зелье (для AJAX-запросов)
     * Collects the finished potion (for AJAX requests).
     *
     * @param Request $request Запрос, содержащий 'unique_id' зелья для сбора // Request containing 'unique_id' of the potion to collect
     * @return \Illuminate\Http\JsonResponse Результат операции // Operation result
     */
    public function collectPotion(Request $request)
    {
        // Проверка авторизации // Authorization check
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
        }

        // Логируем входящий запрос для отладки
        \Log::info('Запрос на сбор зелья (AJAX)', [
            'request_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        // Вызываем общий метод для сбора зелья
        $result = $this->processCollectPotion($request->input('unique_id'));

        // Возвращаем JSON-ответ
        return response()->json($result);
    }

    /**
     * Собирает готовое зелье и перенаправляет на страницу алхимика (для обычных POST-запросов)
     * Collects the finished potion and redirects to the alchemist page (for regular POST requests).
     *
     * @param Request $request Запрос, содержащий 'brewing_id' зелья для сбора
     * @return \Illuminate\Http\RedirectResponse Перенаправление на страницу алхимика
     */
    public function collectPotionRedirect(Request $request)
    {
        // Проверка авторизации
        if (!Auth::check()) {
            return redirect()->route('auth.login')->with('error', 'Сначала авторизуйтесь.');
        }

        // Логируем входящий запрос для отладки
        \Log::info('Запрос на сбор зелья (POST с перенаправлением)', [
            'request_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        // Вызываем общий метод для сбора зелья
        $result = $this->processCollectPotion($request->input('brewing_id'));

        // Перенаправляем на страницу алхимика с соответствующим сообщением
        if ($result['success']) {
            return redirect()->route('masters.alchemist')->with('success', $result['message']);
        } else {
            return redirect()->route('masters.alchemist')->with('error', $result['message']);
        }
    }

    /**
     * Общий метод для обработки сбора зелья
     * Common method for processing potion collection.
     *
     * @param string $brewingId ID варки зелья
     * @return array Результат операции
     */
    private function processCollectPotion($brewingId)
    {
        $user = Auth::user(); // Текущий пользователь // Current user

        // Логируем полученные данные для отладки
        \Log::info('Обработка сбора зелья', [
            'user_id' => $user->id,
            'brewing_id' => $brewingId
        ]);

        // Проверяем, что ID варки предоставлен
        if (!$brewingId) {
            $this->addAlchemyLog($user->id, "Попытка собрать зелье без указания ID варки", false);
            \Log::warning('Попытка собрать зелье без указания ID варки', ['user_id' => $user->id]);
            return ['success' => false, 'message' => 'ID варки не предоставлен'];
        }

        // Сначала получаем запись о варке без проверки статуса, чтобы узнать текущий статус
        $brewingAny = ActiveBrewing::where('id', $brewingId)
            ->where('user_id', $user->id)
            ->first();

        // Логируем результат поиска варки без проверки статуса
        \Log::info('Результат поиска варки (без проверки статуса)', [
            'user_id' => $user->id,
            'brewing_id' => $brewingId,
            'brewing_found' => $brewingAny ? true : false,
            'brewing_status' => $brewingAny ? $brewingAny->status : null,
            'brewing_completed' => $brewingAny ? $brewingAny->isCompleted() : false
        ]);

        // Если варка найдена, но не в статусе COMPLETED, проверяем, должна ли она быть завершена
        if ($brewingAny) {
            // Проверяем, завершена ли варка по времени
            $isTimeCompleted = Carbon::now()->greaterThanOrEqualTo($brewingAny->finished_at);

            // Если варка должна быть завершена по времени, но статус еще не обновлен
            if ($isTimeCompleted && $brewingAny->status === ActiveBrewing::STATUS_BREWING) {
                $brewingAny->status = ActiveBrewing::STATUS_COMPLETED;
                $brewingAny->save();

                \Log::info('Обновлен статус варки на COMPLETED при сборе зелья', [
                    'brewing_id' => $brewingAny->id,
                    'old_status' => $brewingAny->getOriginal('status'),
                    'new_status' => $brewingAny->status,
                    'finished_at' => $brewingAny->finished_at->format('Y-m-d H:i:s'),
                    'current_time' => Carbon::now()->format('Y-m-d H:i:s')
                ]);
            } else if ($brewingAny->isCompleted() && $brewingAny->status !== ActiveBrewing::STATUS_COMPLETED) {
                // Если isCompleted() возвращает true, но статус не COMPLETED
                $brewingAny->status = ActiveBrewing::STATUS_COMPLETED;
                $brewingAny->save();

                \Log::info('Обновлен статус варки на COMPLETED через isCompleted()', [
                    'brewing_id' => $brewingAny->id,
                    'old_status' => $brewingAny->getOriginal('status'),
                    'new_status' => $brewingAny->status
                ]);
            }
        }

        // Проверяем, есть ли запись о готовом зелье в таблице pending_potions
        $pendingPotion = PendingPotion::where('brewing_id', $brewingId)
            ->where('user_id', $user->id)
            ->first();

        // Логируем результат поиска готового зелья в таблице pending_potions
        \Log::info('Результат поиска готового зелья в таблице pending_potions', [
            'user_id' => $user->id,
            'brewing_id' => $brewingId,
            'pending_potion_found' => $pendingPotion ? true : false,
            'pending_potion_id' => $pendingPotion ? $pendingPotion->id : null
        ]);

        // Если запись о готовом зелье найдена, используем ее данные
        if ($pendingPotion) {
            // Получаем связанную варку
            $brewing = ActiveBrewing::find($brewingId);

            // Логируем результат поиска варки
            \Log::info('Результат поиска варки по ID из pending_potions', [
                'user_id' => $user->id,
                'brewing_id' => $brewingId,
                'brewing_found' => $brewing ? true : false,
                'brewing_status' => $brewing ? $brewing->status : null
            ]);

            // Если варка не найдена, используем данные из pending_potions
            if (!$brewing) {
                \Log::info('Варка не найдена, используем данные из pending_potions', [
                    'user_id' => $user->id,
                    'brewing_id' => $brewingId,
                    'pending_potion_id' => $pendingPotion->id
                ]);
            } else {
                // Если варка найдена, но не в статусе COMPLETED, обновляем статус
                if ($brewing->status !== ActiveBrewing::STATUS_COMPLETED) {
                    $brewing->status = ActiveBrewing::STATUS_COMPLETED;
                    $brewing->save();

                    \Log::info('Обновлен статус варки на COMPLETED при сборе зелья из pending_potions', [
                        'brewing_id' => $brewing->id,
                        'old_status' => $brewing->getOriginal('status'),
                        'new_status' => $brewing->status
                    ]);
                }
            }

            // Получаем связанный рецепт
            $recipe = null;
            if ($pendingPotion->recipe_id) {
                $recipe = PotionRecipe::find($pendingPotion->recipe_id);
            }

            // Если рецепт не найден, но есть варка с рецептом, используем его
            if (!$recipe && $brewing && $brewing->recipe) {
                $recipe = $brewing->recipe;
            }

            // Если рецепт все еще не найден, создаем временный объект с данными из pending_potions
            if (!$recipe) {
                $recipe = (object) [
                    'id' => $pendingPotion->recipe_id,
                    'name' => $pendingPotion->name,
                    'description' => $pendingPotion->description,
                    'effect' => $pendingPotion->effect,
                    'effect_value' => $pendingPotion->effect_value,
                    'effect_duration' => $pendingPotion->effect_duration,
                    'level' => $pendingPotion->level,
                    'color' => $pendingPotion->color,
                    'icon' => $pendingPotion->icon
                ];
            }
        } else {
            // Если запись о готовом зелье не найдена, ищем варку напрямую
            // Теперь получаем запись о варке с проверкой статуса
            $brewing = ActiveBrewing::where('id', $brewingId)
                ->where('user_id', $user->id)
                ->where('status', ActiveBrewing::STATUS_COMPLETED)
                ->first();

            // Логируем результат поиска варки с проверкой статуса
            \Log::info('Результат поиска варки с проверкой статуса (без pending_potions)', [
                'user_id' => $user->id,
                'brewing_id' => $brewingId,
                'brewing_found' => $brewing ? true : false,
                'brewing_status' => $brewing ? $brewing->status : null
            ]);

            // Если варка с таким ID не найдена или не в статусе "завершено" // If brewing with this ID is not found or not in "completed" status
            if (!$brewing) {
                // Логгируем попытку собрать несуществующее зелье // Log attempt to collect non-existent potion
                $this->addAlchemyLog($user->id, "Попытка собрать несуществующее или уже собранное зелье (ID: {$brewingId})", false);
                return ['success' => false, 'message' => 'Готовое зелье не найдено или уже собрано'];
            }

            // Получаем связанный рецепт // Get related recipe
            $recipe = $brewing->recipe;

            // Проверяем, что рецепт существует
            if (!$recipe) {
                $this->addAlchemyLog($user->id, "Ошибка доступа к данным рецепта для варки ID: {$brewingId}", false);
                return ['success' => false, 'message' => 'Ошибка доступа к данным рецепта. Возможно, рецепт поврежден.'];
            }
        }

        // Начинаем транзакцию БД // Start DB transaction
        DB::beginTransaction();
        try {
            // Логируем информацию о рецепте для отладки
            \Log::info('Информация о рецепте для создания зелья', [
                'recipe_id' => $recipe->id,
                'recipe_name' => $recipe->name,
                'recipe_icon' => $recipe->icon,
                'recipe_icon_path' => $recipe->icon_path,
                'brewing_quality' => $brewing->quality
            ]);

            // Сначала ищем существующий шаблон зелья в админке
            // Расширенный поиск шаблона зелья с учетом recipe_id для более точного соответствия
            $existingTemplate = Potion::where('recipe_id', $brewing->recipe_id)
                ->where('is_template', true)
                ->where('user_id', null)
                ->first();

            // Если не нашли по recipe_id, ищем по имени и эффекту
            if (!$existingTemplate) {
                $existingTemplate = Potion::where('name', $recipe->name)
                    ->where('effect', $recipe->effect)
                    ->where('level', $recipe->level)
                    ->where('is_template', true)
                    ->where('user_id', null)
                    ->first();
            }

            // Логируем результат поиска шаблона
            \Log::info('Результат поиска шаблона зелья', [
                'recipe_id' => $brewing->recipe_id,
                'recipe_name' => $recipe->name,
                'template_found' => $existingTemplate ? true : false,
                'template_id' => $existingTemplate ? $existingTemplate->id : null
            ]);

            if ($existingTemplate) {
                \Log::info('Найден существующий шаблон зелья в админке', [
                    'template_id' => $existingTemplate->id,
                    'template_name' => $existingTemplate->name,
                    'template_icon' => $existingTemplate->icon,
                    'template_icon_path' => $existingTemplate->getIconPathAttribute()
                ]);

                // Используем существующий шаблон, но обновляем качество если нужно
                if ($existingTemplate->quality !== $brewing->quality) {
                    $existingTemplate->quality = $brewing->quality;
                    $existingTemplate->save();
                }

                // Если у шаблона нет recipe_id, добавляем его
                if (!$existingTemplate->recipe_id && $brewing->recipe_id) {
                    $existingTemplate->recipe_id = $brewing->recipe_id;
                    $existingTemplate->save();

                    \Log::info('Обновлен recipe_id в шаблоне зелья', [
                        'template_id' => $existingTemplate->id,
                        'recipe_id' => $brewing->recipe_id
                    ]);
                }

                $potionTemplate = $existingTemplate;
            } else {
                // Если шаблон не найден, используем иконку рецепта
                $potionIcon = $recipe->icon; // Используем иконку рецепта напрямую

                \Log::info('Создаем новый шаблон зелья', [
                    'recipe_name' => $recipe->name,
                    'recipe_icon' => $recipe->icon,
                    'potion_icon' => $potionIcon
                ]);

                // Создаем шаблон зелья в таблице 'potions'
                $potionTemplate = Potion::create([
                    'name' => $recipe->name,
                    'effect' => $recipe->effect,
                    'level' => $recipe->level,
                    'is_template' => true,
                    'user_id' => null,
                    'recipe_id' => $brewing->recipe_id,
                    'description' => $recipe->description,
                    'quality' => $brewing->quality,
                    'icon' => $potionIcon,
                    'color' => $recipe->color,
                    'effect_value' => $recipe->effect_value,
                    'effect_duration' => $recipe->effect_duration,
                    'uses_left' => 1,
                    'price_gold' => $recipe->price_gold ?? 0,
                    'price_silver' => $recipe->price_silver ?? 0,
                    'price_bronze' => $recipe->price_bronze ?? 0,
                ]);

                // Логируем информацию о созданном шаблоне зелья
                \Log::info('Создан шаблон зелья', [
                    'template_id' => $potionTemplate->id,
                    'template_name' => $potionTemplate->name,
                    'template_icon' => $potionTemplate->icon,
                    'template_icon_path' => $potionTemplate->getIconPathAttribute(),
                    'icon_original' => $potionIcon,
                    'recipe_icon' => $recipe->icon,
                    'recipe_icon_path' => $recipe->icon_path
                ]);
            }

            // Проверяем, что шаблон зелья действительно является шаблоном
            if (!$potionTemplate->is_template) {
                \Log::warning('Попытка создать зелье пользователя из не-шаблона', [
                    'template_id' => $potionTemplate->id,
                    'template_name' => $potionTemplate->name,
                    'is_template' => $potionTemplate->is_template,
                    'user_id' => $potionTemplate->user_id
                ]);

                // Исправляем шаблон, если он не помечен как шаблон
                $potionTemplate->is_template = true;
                $potionTemplate->user_id = null;
                $potionTemplate->save();

                \Log::info('Шаблон зелья исправлен', [
                    'template_id' => $potionTemplate->id,
                    'template_name' => $potionTemplate->name,
                    'is_template' => $potionTemplate->is_template
                ]);
            }

            // Создаем зелье пользователя на основе шаблона
            \Log::info('Начинаем создание зелья пользователя из шаблона', [
                'user_id' => $user->id,
                'template_id' => $potionTemplate->id,
                'template_name' => $potionTemplate->name,
                'template_is_template' => $potionTemplate->is_template,
                'template_recipe_id' => $potionTemplate->recipe_id
            ]);

            $userPotion = UserPotion::createFromTemplate($potionTemplate, $user->id, 'inventory');

            // Логируем создание зелья пользователя
            \Log::info('Создано зелье пользователя', [
                'user_id' => $user->id,
                'potion_template_id' => $potionTemplate->id,
                'user_potion_id' => $userPotion->id,
                'potion_name' => $userPotion->name,
                'quality' => $userPotion->quality,
                'stack_id' => $userPotion->stack_id,
                'stack_position' => $userPotion->stack_position,
                'potion_id' => $userPotion->potion_id
            ]);

            // Обновляем статус варки на "собрано" // Update brewing status to "collected"
            if ($brewing) {
                $brewing->collect();
            }

            // Удаляем запись о готовом зелье из таблицы pending_potions
            if (isset($pendingPotion)) {
                \Log::info('Удаляем запись о готовом зелье из таблицы pending_potions', [
                    'user_id' => $user->id,
                    'brewing_id' => $brewingId,
                    'pending_potion_id' => $pendingPotion->id
                ]);

                $pendingPotion->delete();
            }

            // Начисляем опыт алхимии пользователю // Grant alchemy experience to the user
            $experienceGained = $this->calculateExperience($recipe->level, $brewing ? $brewing->quality : 'Обычное'); // Рассчитываем опыт // Calculate experience
            if ($user->profile) { // Проверяем, существует ли профиль // Check if profile exists
                $user->profile->increment('alchemy_experience', $experienceGained); // Увеличиваем опыт // Increment experience
                // Здесь можно добавить логику проверки повышения уровня // Logic for level up check can be added here
                // $this->checkAlchemyLevelUp($user->profile);
            }

            DB::commit(); // Подтверждаем транзакцию // Commit the transaction

            // Логгируем успешный сбор зелья и получение опыта // Log successful potion collection and experience gain
            $potionQuality = $brewing ? $brewing->quality : (isset($pendingPotion) ? $pendingPotion->quality : 'Обычное');
            $this->addAlchemyLog($user->id, "Собрано зелье: {$recipe->name} ({$potionQuality}). Получено {$experienceGained} опыта.", true);

            // Логируем информацию о созданном зелье пользователя перед возвратом ответа
            \Log::info('Информация о созданном зелье пользователя перед возвратом ответа', [
                'user_potion_id' => $userPotion->id,
                'user_potion_name' => $userPotion->name,
                'user_potion_icon' => $userPotion->icon,
                'user_potion_icon_path' => $userPotion->icon_path,
                'user_potion_stack_id' => $userPotion->stack_id,
                'user_potion_stack_position' => $userPotion->stack_position,
                'template_id' => $potionTemplate->id,
                'template_icon' => $potionTemplate->icon,
                'template_icon_path' => $potionTemplate->icon_path,
                'recipe_icon' => $recipe->icon,
                'recipe_icon_path' => $recipe->icon_path
            ]);

            // Возвращаем успешный ответ с данными о собранном зелье // Return success response with collected potion data
            return [
                'success' => true,
                'message' => "Зелье \"{$recipe->name}\" ({$brewing->quality}) успешно собрано!", // Сообщение // Message
                'experience_gained' => $experienceGained, // Полученный опыт // Experience gained
                'potion' => $userPotion->toArray(), // Данные созданного зелья // Created potion data
                'potion_icon_path' => $userPotion->icon_path // Полный путь к иконке зелья // Full path to potion icon
            ];

        } catch (\Exception $e) {
            // Обработка ошибки // Error handling
            DB::rollBack(); // Откат транзакции // Rollback transaction
            // Логгируем ошибку // Log error
            \Log::error('Ошибка при сборе зелья: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'brewing_id' => $brewingId,
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            // Формируем понятное сообщение об ошибке для пользователя
            $errorMessage = 'Произошла ошибка при сборе зелья.';

            if (strpos($e->getMessage(), "Attempt to read property") !== false) {
                $errorMessage = 'Ошибка доступа к данным рецепта. Возможно, рецепт поврежден или у ингредиента отсутствует связанный ресурс.';
            } elseif (strpos($e->getMessage(), "resource_id") !== false) {
                $errorMessage = 'Ошибка в данных ингредиента: отсутствует связанный ресурс.';
            }

            // Добавляем запись об ошибке в лог алхимии // Add error entry to alchemy log
            $recipeName = $recipe ? $recipe->name : "ID: {$brewingId}";
            $this->addAlchemyLog($user->id, "Ошибка при сборе зелья {$recipeName}: {$errorMessage}", false);

            // Возвращаем ответ об ошибке // Return error response
            return [
                'success' => false,
                'message' => $errorMessage,
                'details' => config('app.debug') ? $e->getMessage() : null
            ];
        }
    }

    /**
     * Отменяет текущий процесс варки зелья
     * Cancels the current potion brewing process.
     *
     * @param Request $request Запрос
     * @return \Illuminate\Http\JsonResponse Результат операции в формате JSON
     */
    public function cancelBrewing(Request $request)
    {
        // Проверка авторизации пользователя
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
        }

        $user = Auth::user();
        $brewingId = $request->input('brewing_id');

        // Если передан ID варки, пытаемся отменить конкретную варку
        if ($brewingId) {
            // Проверяем, есть ли запись о готовом зелье в таблице pending_potions
            $pendingPotion = PendingPotion::where('brewing_id', $brewingId)
                ->where('user_id', $user->id)
                ->first();

            if ($pendingPotion) {
                // Получаем информацию о рецепте
                $recipeName = $pendingPotion->name;

                // Удаляем запись о готовом зелье
                $pendingPotion->delete();

                // Логгируем отмену сбора зелья
                $this->addAlchemyLog($user->id, "Отменен сбор готового зелья: {$recipeName}", false);

                return response()->json(['success' => true, 'message' => 'Готовое зелье удалено']);
            }

            // Если записи в pending_potions нет, ищем варку напрямую
            $brewing = ActiveBrewing::where('id', $brewingId)
                ->where('user_id', $user->id)
                ->first();

            if ($brewing) {
                // Получаем информацию о рецепте
                $recipe = $brewing->recipe;
                $recipeName = $recipe ? $recipe->name : "ID: {$brewingId}";

                // Удаляем запись о варке
                $brewing->delete();

                // Логгируем отмену варки
                $this->addAlchemyLog($user->id, "Отменено приготовление: {$recipeName}", false);

                return response()->json(['success' => true, 'message' => 'Процесс варки отменен']);
            }

            return response()->json(['success' => false, 'message' => 'Указанная варка не найдена']);
        }

        // Если ID варки не передан, пытаемся отменить активную варку
        $activeBrewing = ActiveBrewing::getActiveBrewingForUser($user->id);

        if (!$activeBrewing) {
            return response()->json(['success' => false, 'message' => 'Нет активного процесса варки']);
        }

        // Получаем информацию о рецепте
        $recipe = $activeBrewing->recipe;
        $recipeName = $recipe ? $recipe->name : "ID: {$activeBrewing->id}";

        // Удаляем запись о варке
        $activeBrewing->delete();

        // Логгируем отмену варки
        $this->addAlchemyLog($user->id, "Отменено приготовление: {$recipeName}", false);

        return response()->json(['success' => true, 'message' => 'Процесс варки отменен']);
    }

    /**
     * Отображает страницу улучшения зелий
     * Displays the potion enhancement page.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function enhancement()
    {
        // Проверка авторизации пользователя // Check user authorization
        if (!Auth::check()) {
            return redirect()->route('auth.login')->with('error', 'Сначала авторизуйтесь.');
        }

        $user = Auth::user(); // Получаем текущего пользователя // Get the current user
        $userProfile = $user->profile; // Получаем профиль пользователя // Get user profile

        // Получаем зелья пользователя
        $userPotions = Potion::where('user_id', $user->id)
            ->where('location', 'inventory')
            ->where('quantity', '>', 0)
            ->get();

        // Получаем катализаторы пользователя
        $userCatalysts = UserCatalyst::where('user_id', $user->id)
            ->where('quantity', '>', 0)
            ->with('catalyst')
            ->get();

        // Получаем логи алхимии пользователя
        $alchemyLogs = AlchemyLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('masters.alchemist_ench', [
            'userPotions' => $userPotions,
            'userCatalysts' => $userCatalysts,
            'alchemyLogs' => $alchemyLogs,
        ]);
    }

    /**
     * Улучшает существующее зелье с использованием катализатора
     * Enhances an existing potion using a catalyst.
     *
     * @param Request $request Запрос с параметрами potion_id и catalyst_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function enhancePotion(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
        }

        $user = Auth::user();

        // Валидация входных данных
        $validator = Validator::make($request->all(), [
            'potion_id' => 'required|integer|exists:potions,id,user_id,' . $user->id,
            'catalyst_id' => 'required|integer|exists:alchemy_catalysts,id',
        ]);

        if ($validator->fails()) {
            $this->addAlchemyLog($user->id, "Ошибка при улучшении зелья: " . $validator->errors()->first(), false);
            return response()->json([
                'success' => false,
                'message' => 'Ошибка валидации: ' . $validator->errors()->first()
            ]);
        }

        // Получаем зелье и катализатор
        $potion = Potion::where('id', $request->potion_id)
            ->where('user_id', $user->id)
            ->first();

        $catalyst = AlchemyCatalyst::find($request->catalyst_id);

        // Проверяем, есть ли у пользователя этот катализатор
        $userCatalyst = UserCatalyst::where('user_id', $user->id)
            ->where('alchemy_catalyst_id', $catalyst->id)
            ->first();

        if (!$userCatalyst || $userCatalyst->quantity <= 0) {
            $this->addAlchemyLog($user->id, "Попытка улучшить зелье без катализатора", false);
            return response()->json([
                'success' => false,
                'message' => 'У вас нет этого катализатора'
            ]);
        }

        // Проверяем возможность улучшения зелья
        $qualityLevels = array_keys(Potion::QUALITY_LEVELS);
        $currentQualityIndex = array_search($potion->quality, $qualityLevels);

        if ($currentQualityIndex === false || $currentQualityIndex >= count($qualityLevels) - 1) {
            $this->addAlchemyLog($user->id, "Попытка улучшить зелье максимального качества", false);
            return response()->json([
                'success' => false,
                'message' => 'Зелье уже имеет максимальное качество'
            ]);
        }

        // Определяем шанс успеха в зависимости от редкости катализатора
        $successChance = match ($catalyst->rarity) {
            1 => 50,     // Обычный катализатор - 50% шанс
            2 => 70,     // Необычный катализатор - 70% шанс
            3 => 85,     // Редкий катализатор - 85% шанс
            4 => 95,     // Эпический катализатор - 95% шанс
            5 => 100,    // Легендарный катализатор - 100% шанс
            default => 50 // По умолчанию 50%
        };

        // Расходуем катализатор
        UserCatalyst::removeFromUser($user->id, $catalyst->id);

        // Определяем успех улучшения
        $isSuccess = rand(1, 100) <= $successChance;

        if ($isSuccess) {
            // Улучшаем качество зелья
            $newQuality = $qualityLevels[$currentQualityIndex + 1];
            $potion->quality = $newQuality;
            $potion->save();

            $this->addAlchemyLog(
                $user->id,
                "Зелье \"{$potion->name}\" успешно улучшено до качества \"{$newQuality}\" с помощью катализатора \"{$catalyst->name}\"",
                true
            );

            return response()->json([
                'success' => true,
                'message' => "Зелье успешно улучшено до качества \"{$newQuality}\"",
                'new_quality' => $newQuality,
                'potion' => $potion
            ]);
        } else {
            $this->addAlchemyLog(
                $user->id,
                "Не удалось улучшить зелье \"{$potion->name}\" с помощью катализатора \"{$catalyst->name}\"",
                false
            );

            return response()->json([
                'success' => false,
                'message' => 'Не удалось улучшить зелье. Катализатор израсходован.'
            ]);
        }
    }

    /**
     * Добавляет запись в лог алхимии пользователя в базе данных.
     * Adds an entry to the user's alchemy log in the database.
     *
     * @param int $userId ID пользователя // User ID
     * @param string $message Сообщение лога // Log message
     * @param bool $success Статус успеха операции (true/false) // Success status of the operation (true/false)
     */
    private function addAlchemyLog($userId, $message, $success = true)
    {
        // Создаем новую запись в таблице alchemy_logs // Create a new record in the alchemy_logs table
        AlchemyLog::create([
            'user_id' => $userId, // ID пользователя // User ID
            'message' => $message, // Сообщение // Message
            'success' => $success, // Статус успеха // Success status
        ]);

        // Удаляем старые записи, если их больше 20 // Delete old records if there are more than 20
        $logsCount = AlchemyLog::where('user_id', $userId)->count();
        if ($logsCount > 20) {
            // Получаем ID 20 самых новых записей // Get IDs of the 20 newest records
            $keepLogIds = AlchemyLog::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->pluck('id');

            // Удаляем все остальные записи // Delete all other records
            AlchemyLog::where('user_id', $userId)
                ->whereNotIn('id', $keepLogIds)
                ->delete();
        }
    }

    /**
     * Очищает лог алхимии пользователя
     * Clears the user's alchemy log.
     *
     * @return \Illuminate\Http\JsonResponse Результат операции // Operation result
     */
    public function clearLogs()
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
        }
        $userId = Auth::id();

        // Удаляем все записи логов пользователя из базы данных // Delete all user's log records from the database
        AlchemyLog::where('user_id', $userId)->delete();

        // Добавляем запись об очистке лога // Add log clearing entry
        $this->addAlchemyLog($userId, "Логи алхимии очищены.", true);

        return response()->json(['success' => true, 'message' => 'Логи алхимии очищены.']);
    }

    /**
     * Проверяет наличие ингредиентов для выбранного рецепта
     * Checks if the user has the required ingredients for the selected recipe.
     *
     * @param Request $request Запрос с ID рецепта
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkIngredients(Request $request)
    {
        try {
            if (!Auth::check()) {
                return response()->json(['success' => false, 'message' => 'Требуется авторизация']);
            }

            $user = Auth::user();
            $userProfile = $user->profile;

            // Валидация входных данных
            $validator = Validator::make($request->all(), [
                'recipe_id' => 'required|integer|exists:potion_recipes,id',
            ]);

            if ($validator->fails()) {
                return response()->json(['success' => false, 'message' => 'Некорректный ID рецепта']);
            }

            // Получаем рецепт с загрузкой связанных ингредиентов и их ресурсов
            $recipe = PotionRecipe::with(['ingredients', 'ingredients.resource'])->find($request->recipe_id);
            if (!$recipe) {
                return response()->json(['success' => false, 'message' => 'Рецепт не найден']);
            }

            // Получаем информацию о количестве рецептов у пользователя
            $userRecipe = $user->userRecipes()
                ->where('potion_recipe_id', $request->recipe_id)
                ->where('location', 'inventory')
                ->where('quantity', '>', 0)
                ->first();

            $recipeQuantity = $userRecipe ? $userRecipe->quantity : 0;

            // Проверяем уровень алхимии пользователя
            $alchemyLevel = $userProfile->alchemy_level ?? 1;
            $canBrewLevel = $alchemyLevel >= $recipe->min_alchemy_level;

            if (!$canBrewLevel) {
                return response()->json([
                    'success' => false,
                    'message' => "Требуется уровень алхимии: {$recipe->min_alchemy_level}",
                    'required_level' => $recipe->min_alchemy_level,
                    'user_level' => $alchemyLevel
                ]);
            }

            // Проверяем наличие активной варки
            $activeBrewing = ActiveBrewing::getActiveBrewingForUser($user->id);
            if ($activeBrewing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Алхимический стол занят',
                    'active_brewing' => true
                ]);
            }

            // Получаем ресурсы пользователя из инвентаря
            $userResources = UserResource::where('user_id', $user->id)
                ->where('location', 'inventory')
                ->where('quantity', '>', 0)
                ->with('resource')
                ->get()
                ->groupBy('resource_id')
                ->map(function ($items) {
                    $firstItem = $items->first();
                    $totalQuantity = $items->sum('quantity');
                    return (object) [
                        'resource' => $firstItem->resource,
                        'total_quantity' => $totalQuantity,
                    ];
                });

            // Получаем алхимические ингредиенты пользователя
            $userAlchemyIngredients = UserAlchemyIngredient::where('user_id', $user->id)
                ->where('quantity', '>', 0)
                ->with('ingredient')
                ->get()
                ->groupBy('alchemy_ingredient_id')
                ->map(function ($items) {
                    $firstItem = $items->first();
                    $totalQuantity = $items->sum('quantity');
                    return (object) [
                        'ingredient' => $firstItem->ingredient,
                        'total_quantity' => $totalQuantity,
                    ];
                });

            // Проверяем наличие всех необходимых ингредиентов
            $canBrewIngredients = true;
            $missingIngredients = [];
            $ingredientsInfo = [];

            foreach ($recipe->ingredients as $ingredient) {
                // Получаем ID алхимического ингредиента
                $ingredientId = $ingredient->id;
                // Получаем ID ресурса из ингредиента (может быть null)
                $resourceId = $ingredient->resource_id;

                // Ищем алхимический ингредиент у пользователя напрямую
                $userAlchemyIngredient = $userAlchemyIngredients->first(function ($item) use ($ingredientId) {
                    return $item->ingredient->id === $ingredientId;
                });

                // Определяем количество алхимических ингредиентов
                $alchemyIngredientQuantity = $userAlchemyIngredient ? $userAlchemyIngredient->total_quantity : 0;

                // Получаем количество ингредиентов напрямую из базы данных для проверки
                $directAlchemyIngredientQuantity = UserAlchemyIngredient::where('user_id', $user->id)
                    ->where('alchemy_ingredient_id', $ingredientId)
                    ->where('location', 'inventory')
                    ->where('quantity', '>', 0)
                    ->sum('quantity');

                // Если есть расхождение, используем значение напрямую из базы данных
                if ($directAlchemyIngredientQuantity != $alchemyIngredientQuantity) {
                    \Log::warning('Расхождение в количестве ингредиентов', [
                        'ingredient_id' => $ingredientId,
                        'ingredient_name' => $ingredient->name,
                        'cached_quantity' => $alchemyIngredientQuantity,
                        'direct_quantity' => $directAlchemyIngredientQuantity
                    ]);
                    $alchemyIngredientQuantity = $directAlchemyIngredientQuantity;
                }

                // Инициализируем количество ресурсов
                $resourceQuantity = 0;

                // Если у ингредиента есть связанный ресурс, ищем его в инвентаре пользователя
                if ($resourceId) {
                    $userResource = $userResources->first(function ($item) use ($resourceId) {
                        return $item->resource->id === $resourceId;
                    });
                    $resourceQuantity = $userResource ? $userResource->total_quantity : 0;
                }

                // Суммируем количество ресурсов и алхимических ингредиентов
                $userQuantity = $resourceQuantity + $alchemyIngredientQuantity;
                $neededQuantity = $ingredient->pivot->quantity;

                // Логируем для отладки
                \Log::info('Проверка ингредиента для рецепта', [
                    'ingredient_id' => $ingredientId,
                    'ingredient_name' => $ingredient->name,
                    'resource_id' => $resourceId,
                    'resource_quantity' => $resourceQuantity,
                    'alchemy_ingredient_quantity' => $alchemyIngredientQuantity,
                    'total_user_quantity' => $userQuantity,
                    'needed_quantity' => $neededQuantity
                ]);

                // Получаем путь к иконке ингредиента
                $iconPath = $this->getIngredientIconPath($ingredient);

                // Проверяем, достаточно ли ингредиентов
                $hasEnough = $userQuantity >= $neededQuantity;

                $ingredientsInfo[] = [
                    'id' => $ingredient->id,
                    'name' => $ingredient->name,
                    'icon_path' => $iconPath,
                    'required_quantity' => $neededQuantity,
                    'user_quantity' => $userQuantity,
                    'has_enough' => $hasEnough
                ];

                if (!$hasEnough) {
                    $canBrewIngredients = false;
                    // Вычисляем, сколько ингредиентов не хватает
                    $missingAmount = $neededQuantity - $userQuantity;
                    $missingIngredients[] = [
                        'name' => $ingredient->name,
                        'missing' => $missingAmount
                    ];
                }
            }

            // Определяем иконку для зелья (аналогично другим методам)
            $potionIcon = 'potions/smallBottleHP.png'; // Иконка по умолчанию

            // Сначала ищем существующий шаблон зелья для этого рецепта
            $potionTemplate = Potion::where('recipe_id', $recipe->id)
                ->where('is_template', true)
                ->where('user_id', null)
                ->first();

            if ($potionTemplate && !empty($potionTemplate->icon)) {
                // Если нашли шаблон зелья, используем его иконку
                $potionIcon = $potionTemplate->icon;

                // Если путь не содержит 'potions/', добавляем его
                if (strpos($potionIcon, 'potions/') === false) {
                    $potionIcon = 'potions/' . basename($potionIcon);
                }

                \Log::info('Используем иконку из существующего шаблона зелья для проверки ингредиентов', [
                    'recipe_id' => $recipe->id,
                    'template_id' => $potionTemplate->id,
                    'template_icon' => $potionTemplate->icon,
                    'potion_icon' => $potionIcon
                ]);
            }
            // Если шаблон не найден, пробуем преобразовать иконку рецепта
            else if (!empty($recipe->icon)) {
                // Получаем только имя файла из пути рецепта
                $iconFileName = basename($recipe->icon);

                // Проверяем, содержит ли путь к иконке рецепта "Recipe" или "recipe"
                if (stripos($recipe->icon, 'recipe') !== false) {
                    // Если это иконка рецепта, заменяем на соответствующую иконку зелья
                    $potionIcon = str_ireplace('recipe', 'potion', $iconFileName);
                    // Добавляем путь к папке с иконками зелий
                    $potionIcon = 'potions/' . $potionIcon;
                } else {
                    // Если путь содержит 'icons/recipes/', заменяем на 'potions/'
                    if (stripos($recipe->icon, 'icons/recipes/') !== false) {
                        // Получаем имя файла и заменяем "Recipe" на "Potion" если есть
                        $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                        // Добавляем путь к папке с иконками зелий
                        $potionIcon = 'potions/' . $potionIcon;
                    } else {
                        // Проверяем, есть ли в имени файла слово "Recipe"
                        if (stripos($iconFileName, 'Recipe') !== false) {
                            // Заменяем "Recipe" на "Potion"
                            $potionIcon = str_ireplace('Recipe', 'Potion', $iconFileName);
                            // Добавляем путь к папке с иконками зелий
                            $potionIcon = 'potions/' . $potionIcon;
                        } else {
                            // Если это не иконка рецепта, используем стандартную иконку зелья
                            $potionIcon = 'potions/smallBottleHP.png';
                        }
                    }
                }
            }

            // Логируем информацию об иконке для отладки
            \Log::info('Определена иконка для зелья при проверке ингредиентов', [
                'recipe_id' => $recipe->id,
                'recipe_name' => $recipe->name,
                'recipe_icon' => $recipe->icon,
                'recipe_icon_path' => $recipe->icon_path,
                'potion_icon' => $potionIcon,
                'potion_icon_path' => asset('assets/' . $potionIcon)
            ]);

            // Формируем сообщение о недостающих ингредиентах
            $missingIngredientsMessage = '';
            if (!$canBrewIngredients) {
                $missingIngredientsMessage = 'Не хватает: ' . implode(', ', array_map(function ($item) {
                    // Отображаем только количество, которого не хватает
                    return "{$item['name']} ({$item['missing']} шт.)";
                }, $missingIngredients));
            }

            return response()->json([
                'success' => true,
                'can_brew' => $canBrewIngredients,
                'recipe' => [
                    'id' => $recipe->id,
                    'name' => $recipe->name,
                    'description' => $recipe->description,
                    'icon_path' => asset('assets/' . $potionIcon), // Используем иконку зелья вместо рецепта
                    'brewing_time' => $recipe->brewing_time,
                    'brewing_time_formatted' => $this->formatBrewingTime($recipe->brewing_time),
                    'quantity' => $recipeQuantity // Добавляем количество рецептов у пользователя
                ],
                'ingredients' => $ingredientsInfo,
                'missing_ingredients' => $missingIngredients,
                'message' => $canBrewIngredients ? 'Можно создать зелье' : $missingIngredientsMessage
            ]);
        } catch (\Exception $e) {
            // Логируем ошибку
            \Log::error('Ошибка при проверке ингредиентов: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'recipe_id' => $request->recipe_id ?? 'не указан',
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            // Возвращаем JSON-ответ с ошибкой
            return response()->json([
                'success' => false,
                'message' => 'Произошла ошибка при проверке ингредиентов: ' . $e->getMessage(),
                'error_code' => 'ERR_CHECK_INGREDIENTS'
            ]);
        }
    }

    /**
     * Получает список рецептов зелий (устаревший метод, больше не используется в index)
     * Gets the list of potion recipes (deprecated method, no longer used in index).
     * @deprecated Этот метод больше не используется напрямую в index, рецепты берутся из PotionRecipe::class
     * This method is no longer used directly in index, recipes are fetched from PotionRecipe::class
     * @return array Массив рецептов // Array of recipes
     */
    private function getPotionRecipes()
    {
        // Загружаем активные рецепты из базы данных с ингредиентами и ресурсами
        // Load active recipes from the database with ingredients and resources
        $recipes = PotionRecipe::where('is_active', true)
            ->with(['ingredients', 'ingredients.resource']) // Загружаем ингредиенты и связанные ресурсы // Load ingredients and related resources
            ->get(); // Получаем коллекцию // Get collection

        // Преобразуем коллекцию Eloquent в массив для совместимости (если требуется) // Convert Eloquent collection to array for compatibility (if needed)
        return $recipes->map(function ($recipe) {
            return [
                'id' => $recipe->id, // ID рецепта // Recipe ID
                'name' => $recipe->name, // Название // Name
                'description' => $recipe->description, // Описание // Description
                'quality' => $recipe->quality, // Качество // Quality
                'icon' => $recipe->icon, // Иконка // Icon
                'icon_path' => $recipe->icon_path, // Полный путь к иконке // Full icon path
                'color' => $recipe->color, // Цвет // Color
                'level' => intval($recipe->level), // Уровень (число) // Level (number)
                'required_level' => $recipe->min_alchemy_level, // Требуемый уровень алхимии // Required alchemy level
                'effect' => $recipe->effect, // Эффект // Effect
                'effect_value' => $recipe->effect_value, // Значение эффекта // Effect value
                'effect_duration' => $recipe->effect_duration, // Длительность эффекта // Effect duration
                'brewing_time' => $recipe->brewing_time, // Базовое время // Base time
                'brewing_time_modifier' => $recipe->brewing_time_modifier, // Модификатор // Modifier
                // Преобразуем коллекцию ингредиентов в массив объектов // Convert ingredients collection to array of objects
                'ingredients' => $recipe->ingredients->map(function ($ingredient) {
                    return (object) [
                        'id' => $ingredient->id, // ID модели AlchemyIngredient // AlchemyIngredient model ID
                        'resource_id' => $ingredient->resource_id, // ID ресурса // Resource ID
                        'name' => $ingredient->name, // Название ингредиента // Ingredient name
                        'quantity' => $ingredient->pivot->quantity, // Требуемое количество // Required quantity
                        'rarity' => $ingredient->rarity, // Редкость // Rarity
                        'icon_path' => $this->getIngredientIconPath($ingredient) // Иконка ресурса // Resource icon
                    ];
                })->toArray(), // Преобразуем в массив // Convert to array
                'is_active' => $recipe->is_active // Статус активности // Active status
            ];
        })->toArray(); // Преобразуем всю коллекцию рецептов в массив // Convert the entire recipe collection to an array
    }

    /**
     * Списывает указанное количество ресурса из инвентаря пользователя
     * Consumes the specified quantity of a resource from the user's inventory.
     *
     * @param int $userId ID пользователя // User ID
     * @param int $resourceId ID ресурса // Resource ID
     * @param int $quantity Количество для списания // Quantity to consume
     * @return array Результат операции ['success' => bool, 'message' => string, 'needed' => int] // Operation result
     */
    /**
     * Списывает указанное количество алхимического ингредиента из инвентаря пользователя
     * Consumes the specified quantity of an alchemy ingredient from the user's inventory.
     *
     * @param int $userId ID пользователя // User ID
     * @param int $ingredientId ID алхимического ингредиента // Alchemy Ingredient ID
     * @param int $quantity Количество для списания // Quantity to consume
     * @return array Результат операции ['success' => bool, 'message' => string, 'needed' => int] // Operation result
     */
    private function consumeAlchemyIngredient($userId, $ingredientId, $quantity)
    {
        if ($quantity <= 0) { // Нельзя списать 0 или меньше // Cannot consume 0 or less
            return ['success' => true, 'message' => 'Количество должно быть положительным', 'needed' => 0];
        }

        // Проверяем, что ingredientId не пустой
        if (!$ingredientId) {
            \Log::error('Ошибка в consumeAlchemyIngredient: ingredientId отсутствует', [
                'user_id' => $userId,
                'ingredient_id' => $ingredientId,
                'quantity' => $quantity
            ]);
            return ['success' => false, 'message' => 'Ошибка данных: ID ингредиента отсутствует', 'needed' => $quantity];
        }

        // Получаем все стаки данного ингредиента у пользователя в инвентаре
        $userIngredients = UserAlchemyIngredient::where('user_id', $userId)
            ->where('alchemy_ingredient_id', $ingredientId)
            ->where('location', 'inventory')
            ->where('quantity', '>', 0)
            ->orderBy('created_at') // Сначала списываем самые старые
            ->get();

        $totalAvailable = $userIngredients->sum('quantity'); // Общее доступное количество

        if ($totalAvailable < $quantity) { // Если доступно меньше, чем нужно
            return ['success' => false, 'message' => 'Недостаточно ингредиента', 'needed' => $quantity - $totalAvailable];
        }

        // Списываем ингредиенты
        $remainingToConsume = $quantity; // Сколько осталось списать
        foreach ($userIngredients as $ingredientStack) { // Перебираем стаки
            if ($remainingToConsume <= 0)
                break; // Если списали достаточно, выходим

            $consumeAmount = min($ingredientStack->quantity, $remainingToConsume); // Сколько списываем из этого стака
            $ingredientStack->quantity -= $consumeAmount; // Уменьшаем количество в стаке
            $remainingToConsume -= $consumeAmount; // Уменьшаем остаток для списания

            if ($ingredientStack->quantity <= 0) { // Если стак опустел
                $ingredientStack->delete(); // Удаляем стак
            } else {
                $ingredientStack->save(); // Сохраняем изменения в стаке
            }
        }

        return ['success' => true, 'message' => 'Ингредиенты списаны', 'needed' => 0]; // Успешное списание
    }

    private function consumeResource($userId, $resourceId, $quantity)
    {
        if ($quantity <= 0) { // Нельзя списать 0 или меньше // Cannot consume 0 or less
            return ['success' => true, 'message' => 'Количество должно быть положительным', 'needed' => 0];
        }

        // Проверяем, что resourceId не пустой
        if (!$resourceId) {
            \Log::error('Ошибка в consumeResource: resourceId отсутствует', [
                'user_id' => $userId,
                'resource_id' => $resourceId,
                'quantity' => $quantity
            ]);
            return ['success' => false, 'message' => 'Ошибка данных: ID ресурса отсутствует', 'needed' => $quantity];
        }

        // Получаем все стаки данного ресурса у пользователя в инвентаре // Get all stacks of this resource for the user in inventory
        $userResources = UserResource::where('user_id', $userId)
            ->where('resource_id', $resourceId)
            ->where('location', 'inventory')
            ->where('quantity', '>', 0)
            ->orderBy('created_at') // Сначала списываем самые старые // Consume oldest first
            ->get(); // Получаем коллекцию стаков // Get the collection of stacks

        $totalAvailable = $userResources->sum('quantity'); // Общее доступное количество // Total available quantity

        // Получаем ингредиент, связанный с этим ресурсом
        $ingredient = AlchemyIngredient::where('resource_id', $resourceId)->first();
        $alchemyIngredientQuantity = 0;

        // Если есть связанный алхимический ингредиент, получаем его количество у пользователя
        if ($ingredient) {
            $userAlchemyIngredients = UserAlchemyIngredient::where('user_id', $userId)
                ->where('alchemy_ingredient_id', $ingredient->id)
                ->where('quantity', '>', 0)
                ->orderBy('created_at')
                ->get();

            $alchemyIngredientQuantity = $userAlchemyIngredients->sum('quantity');
        }

        // Общее доступное количество (ресурсы + алхимические ингредиенты)
        $totalAvailableAll = $totalAvailable + $alchemyIngredientQuantity;

        if ($totalAvailableAll < $quantity) { // Если доступно меньше, чем нужно // If less available than needed
            return ['success' => false, 'message' => 'Недостаточно ресурса', 'needed' => $quantity - $totalAvailableAll];
        }

        // Сначала списываем из обычных ресурсов
        $remainingToConsume = $quantity; // Сколько осталось списать // How much remains to consume
        foreach ($userResources as $resourceStack) { // Перебираем стаки // Iterate through stacks
            if ($remainingToConsume <= 0)
                break; // Если списали достаточно, выходим // If enough consumed, exit

            $consumeAmount = min($resourceStack->quantity, $remainingToConsume); // Сколько списываем из этого стака // How much to consume from this stack
            $resourceStack->quantity -= $consumeAmount; // Уменьшаем количество в стаке // Decrease quantity in stack
            $remainingToConsume -= $consumeAmount; // Уменьшаем остаток для списания // Decrease remaining to consume

            if ($resourceStack->quantity <= 0) { // Если стак опустел // If stack is empty
                $resourceStack->delete(); // Удаляем стак // Delete stack
            } else {
                $resourceStack->save(); // Сохраняем изменения в стаке // Save stack changes
            }
        }

        // Если после списания обычных ресурсов остались еще ингредиенты для списания и есть связанный алхимический ингредиент
        if ($remainingToConsume > 0 && $ingredient) {
            foreach ($userAlchemyIngredients as $ingredientStack) {
                if ($remainingToConsume <= 0)
                    break;

                $consumeAmount = min($ingredientStack->quantity, $remainingToConsume);
                $ingredientStack->quantity -= $consumeAmount;
                $remainingToConsume -= $consumeAmount;

                if ($ingredientStack->quantity <= 0) {
                    $ingredientStack->delete();
                } else {
                    $ingredientStack->save();
                }
            }
        }

        return ['success' => true, 'message' => 'Ресурсы списаны', 'needed' => 0]; // Успешное списание // Successful consumption
    }

    /**
     * Получает путь к иконке ингредиента
     * Gets the path to the ingredient icon.
     *
     * @param AlchemyIngredient $ingredient Ингредиент // Ingredient
     * @return string Путь к иконке // Path to the icon
     */
    private function getIngredientIconPath($ingredient)
    {
        // Путь по умолчанию
        $iconPath = asset('assets/icons/resources/default.png');

        try {
            // Проверяем, есть ли у ингредиента связанный ресурс
            if ($ingredient->resource_id && $ingredient->resource) {
                $iconPath = $ingredient->resource->icon_path ?? asset('assets/icons/resources/default.png');
            } elseif ($ingredient->icon) {
                // Если у ингредиента есть собственная иконка, используем её
                $iconPath = str_starts_with($ingredient->icon, 'assets/')
                    ? asset($ingredient->icon)
                    : asset("assets/icons/alchemy/{$ingredient->icon}");
            } elseif ($ingredient->image_path) {
                // Если есть путь к загруженному изображению
                $iconPath = asset("storage/{$ingredient->image_path}");
            }
        } catch (\Exception $e) {
            // Логируем ошибку, но возвращаем иконку по умолчанию
            \Log::error('Ошибка при получении пути к иконке ингредиента', [
                'ingredient_id' => $ingredient->id ?? 'unknown',
                'ingredient_name' => $ingredient->name ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }

        return $iconPath;
    }

    /**
     * Рассчитывает опыт за варку зелья
     * Calculates experience for brewing a potion.
     *
     * @param string $level Уровень рецепта // Recipe level
     * @param string $quality Качество рецепта/зелья // Recipe/potion quality
     * @return int Количество опыта // Amount of experience
     */
    private function calculateExperience($level, $quality)
    {
        $levelInt = intval($level); // Преобразуем уровень в число, если это строка // Convert level to integer if it's a string

        // Базовый опыт в зависимости от числового уровня // Base experience based on numeric level
        $baseExperience = match (true) {
            $levelInt >= 6 => 25, // Master (6-7)
            $levelInt >= 4 => 12, // Advanced (4-5)
            $levelInt >= 1 => 5, // Basic (1-3)
            default => 3, // На всякий случай // Just in case
        };

        // Множитель опыта в зависимости от качества // Experience multiplier based on quality
        $qualityMultiplier = match ($quality) {
            PotionRecipe::QUALITY_UNCOMMON => 1.2, // Необычное // Uncommon
            PotionRecipe::QUALITY_RARE => 1.5, // Редкое // Rare
            PotionRecipe::QUALITY_EPIC => 2.0, // Эпическое // Epic
            PotionRecipe::QUALITY_LEGENDARY => 3.0, // Легендарное (добавим множитель) // Legendary (add multiplier)
            default => 1.0, // Обычное // Common
        };

        // Рассчитываем и округляем итоговый опыт // Calculate and round final experience
        return (int) round($baseExperience * $qualityMultiplier);
    }
}