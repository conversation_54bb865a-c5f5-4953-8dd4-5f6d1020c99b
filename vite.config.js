import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import { fileURLToPath, URL } from "node:url";

export default defineConfig({
    plugins: [
        laravel({
            input: [
                "resources/css/app.css",
                "resources/css/glow-effects.css",
                "resources/css/custom.css",
                "resources/css/maintenance.css",
                "resources/css/square-mobile.css",
                "resources/css/square-standardized.css",
                "resources/css/market-slots-mobile.css",
                "resources/css/donations.css",
                // Новые CSS файлы для стандартизации
                "resources/css/battle-animations.css",
                "resources/css/battle/tooltips.css",
                "resources/css/inventory.css",
                "resources/css/user-profile.css",
                "resources/css/experience-progress.css",
                // CSS файлы компонентов
                "resources/css/components/guild-invitation.css",
                "resources/css/components/donation-button.css",
                "resources/css/components/adaptive-button.css",
                // CSS файлы для страниц битвы
                "resources/css/battle/mines-selection.css",
                "resources/css/battle/mines-filters.css",
                // CSS файлы для кузницы
                "resources/css/blacksmith/main.css",
                // CSS файлы для чата
                "resources/css/chat/chat-interface.css",
                // CSS файлы для экипировки
                "resources/css/equipment/equipment-main.css",
                "resources/css/equipment/equipment-animations.css",
                "resources/css/equipment/item-quality.css",
                // CSS файлы для подземелий
                "resources/css/dungeons/compact-rewards-carousel.css",
                // CSS файлы для топ игроков
                "resources/css/top-players.css",
                // CSS файлы для полоски опыта
                "resources/css/experience-progress.css",
                // Auth CSS файлы
                "resources/css/auth/character-creation.css",
                // JS файлы
                "resources/js/app.js",
                "resources/js/attackLimiter.js",
                "resources/js/eqItem.js",
                "resources/js/itemEffects.js",
                "resources/js/square-interactions.js",
                // Battle JS файлы
                "resources/js/battle/tooltips.js",
                "resources/js/user/profile-interactions.js",
                "resources/js/user/profile-other-interactions.js",
                "resources/js/battle/mines-selection.js",
                "resources/js/battle/mines-filters.js",
                "resources/js/battle/mines-dropdown-pagination.js",
                // Dungeons JS файлы
                "resources/js/dungeons/rewards-navigation.js",
                "resources/js/dungeons/dungeon-rewards-carousel.js",
                "resources/js/dungeons/compact-rewards-carousel.js",
                "resources/js/dungeons/horizontal-rewards-scroll.js",
                "resources/js/dungeons/lobby.js",
                // Equipment JS файлы
                "resources/js/equipment/equipment-main.js",
                "resources/js/equipment/equipment-interactions.js",
                "resources/js/equipment/item-details.js",
                "resources/js/welcome/welcome.js",
                "resources/js/welcome/notifications.js",
                "resources/js/welcome/utils.js",
                // Глобальные JS файлы
                "resources/js/global/csrf.js",
                "resources/js/global/notifications.js",
                // Модули безопасности
                "resources/js/security/csrf-security.js",
                // Layout JS файлы
                "resources/js/layout/footer-counters.js",
                "resources/js/layout/server-time.js",
                // Home page JS файлы
                "resources/js/home/<USER>",
                "resources/js/home/<USER>",
                // Notifications JS файлы
                "resources/js/notifications/message-notifications.js",
                // Inventory JS файлы
                "resources/js/inventory/inventory-manager.js",
                // Chat JS файлы
                "resources/js/chat/chat-manager.js",
                // Blacksmith JS файлы
                "resources/js/blacksmith/main.js",
                "resources/js/blacksmith/progress-manager.js",
                "resources/js/blacksmith/forge-interface.js",
                "resources/js/blacksmith/melting-system-new.js", // Новая система с PostgreSQL
                "resources/js/blacksmith/confirmation-modal.js",
                // Masters JS файлы
                "resources/js/masters/alchemist.js",
                // Admin JS файлы
                "resources/js/admin/blacksmith.js",
                "resources/js/admin/item-management.js",
                "resources/js/admin/item-form.js",
                "resources/js/admin/modal-manager.js",
                "resources/js/admin/shop-form.js",
                "resources/js/admin/shop-recipe-preview.js",
                "resources/js/admin/testers.js",
                "resources/js/admin/dungeon-rewards.js",
                // Forum JS файлы
                "resources/js/bbcode-toolbar.js",
                "resources/js/forum/forum-main.js",
                "resources/js/forum/topic-management.js",
                "resources/js/forum/post-management.js",
                "resources/js/forum/topic-view.js",
                "resources/js/forum/topic-create.js",
                "resources/js/forum/post-edit.js",
                "resources/js/forum/news-index.js",
                "resources/js/forum/news-view.js",
                "resources/js/forum/news-create.js",
                "resources/js/forum/news-edit.js",
                "resources/js/forum/comment-edit.js",
                "resources/js/forum/comment-management.js",
                // Messages JS файлы
                "resources/js/messages/modal-management.js",
                "resources/js/messages/form-handler.js",
                // Online JS файлы
                "resources/js/online/filters.js",
                "resources/js/online/activity-indicators.js",
                "resources/js/online/unified-status-updater.js",
                //Топ игроков
                "resources/js/top-players/header-filtering.js",
                // Party JS файлы
                "resources/js/party/party-main.js",
                "resources/js/party/party-invitations.js",
                // Auth JS файлы
                "resources/js/auth/character-creation.js",
                // Диагностические JS файлы
                "resources/js/diagnostics/css-loader-check.js",
                "resources/js/diagnostics/production-css-fix.js",
                // Effects JS файлы
                "resources/js/effects/stun-manager.js",
            ],
            refresh: true,
        }),
    ],

    resolve: {
        alias: {
            alpinejs: fileURLToPath(
                new URL("./node_modules/alpinejs", import.meta.url)
            ),
        },
    },

    build: {
        // Продакшн настройки для безопасности
        minify: "terser", // Принудительная минификация
        terserOptions: {
            compress: {
                drop_console: true, // Удаляем console.log
                drop_debugger: true, // Удаляем debugger
                pure_funcs: ["console.log", "console.info", "console.debug"], // Удаляем отладочные функции
            },
            format: {
                comments: false, // Удаляем все комментарии
            },
        },
        sourcemap: false, // Отключаем source maps для продакшн
        rollupOptions: {
            output: {
                manualChunks: undefined,
                // Обфускация имен файлов
                entryFileNames: "assets/[name]-[hash].js",
                chunkFileNames: "assets/[name]-[hash].js",
                assetFileNames: "assets/[name]-[hash].[ext]",
            },
        },
        chunkSizeWarningLimit: 1000,
        // Дополнительные настройки безопасности
        cssCodeSplit: true,
        assetsInlineLimit: 0, // Не инлайним ассеты для лучшего кеширования
    },

    server: {
        hmr: {
            host: "localhost",
        },
        cors: true,
    },

    optimizeDeps: {
        include: ["alpinejs"],
    },

    // Настройки для разработки
    define: {
        __DEV__: JSON.stringify(process.env.NODE_ENV === "development"),
    },
});
